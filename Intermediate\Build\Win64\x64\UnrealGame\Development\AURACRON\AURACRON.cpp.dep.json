{"Version": "1.2", "Data": {"Source": "c:\\prototipo\\auracron\\source\\auracron\\auracron.cpp", "ProvidedModule": "", "PCH": "c:\\prototipo\\auracron\\intermediate\\build\\win64\\x64\\auracron\\development\\engine\\sharedpch.engine.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\prototipo\\auracron\\intermediate\\build\\win64\\x64\\unrealgame\\development\\auracron\\definitions.auracron.h", "c:\\prototipo\\auracron\\source\\auracron\\auracron.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}