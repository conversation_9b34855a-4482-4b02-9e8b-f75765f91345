# 🎮 **AURACRON - GAME DESIGN DOCUMENT**
## **Versão Inicial - Mapa Único**

---

## 📋 **VISÃO GERAL DO JOGO**

### **Conceito Central**
**AURACRON** é um MOBA 5v5 inovador que combina elementos clássicos do gênero com mecânicas únicas de customização e estratégia dinâmica. O jogo se passa em um reino místico onde duas equipes competem pelo controle de um mapa estratégico com elementos únicos.

### **Público-Alvo**
- **Primário**: Jogadores de MOBA (18-35 anos) com experiência em League of Legends, Dota 2, Wild Rift
- **Secundário**: Jogadores casuais interessados em estratégia competitiva
- **Terciário**: Streamers e criadores de conteúdo buscando novos jogos para audiência

### **Pilares de Design**
1. **Acessibilidade Estratégica**: <PERSON><PERSON><PERSON><PERSON> de aprender, difícil de dominar
2. **Inovação Mecânica**: Sistemas únicos que expandem o gênero MOBA
3. **Competitividade Balanceada**: Meta diversificada e estratégias viáveis múltiplas
4. **Inclusão Social**: Ferramentas robustas contra toxicidade
5. **Performance Escalável**: Otimizado para dispositivos entry-level até high-end

---

## 🚀 **MECÂNICAS INOVADORAS**

### **Sistema de Trilhos Dinâmicos**

O mapa principal possui **três trilhos energéticos** que atravessam o campo de batalha:

#### **🌞 Trilho Solar (Superior)**
- **Efeito**: +15% velocidade de movimento, +10% dano de habilidades
- **Ativação**: Controlado pela equipe que possui maioria de torres na lane superior
- **Duração**: 90 segundos, cooldown de 180 segundos
- **Visual**: Partículas douradas fluindo da base até objetivos

#### **⚖️ Trilho Axis (Central)**
- **Efeito**: +20% regeneração de mana, redução de 15% em cooldowns
- **Ativação**: Controlado pela equipe que possui controle da área central (jungle)
- **Duração**: 60 segundos, cooldown de 120 segundos
- **Visual**: Energia azul-prateada conectando pontos estratégicos

#### **🌙 Trilho Lunar (Inferior)**
- **Efeito**: +25% cura recebida, +10% resistência mágica
- **Ativação**: Controlado pela equipe que possui maioria de torres na lane inferior
- **Duração**: 120 segundos, cooldown de 200 segundos
- **Visual**: Energia violeta-prateada com efeitos de lua crescente

### **Fluxo Prismal Central**

No centro do mapa, um **núcleo energético** chamado **Prismal Flow** influencia dinamicamente o campo de batalha:

#### **Estados do Prismal Flow**
- **Dormant (0-10 min)**: Sem efeitos ativos
- **Awakening (10-20 min)**: Trilhos têm 50% mais duração
- **Active (20-30 min)**: Dois trilhos podem estar ativos simultaneamente
- **Critical (30+ min)**: Todos os trilhos podem estar ativos, efeitos amplificados em 25%

#### **Interação com Objetivos**
- **Dragon/Baron equivalents**: Ativar o Prismal Flow temporariamente
- **Torre Central**: Controlar influencia qual trilho ativa primeiro
- **Jungle Camps**: Contribuem para "energia" dos trilhos

### **Sistema Auracron Sigil**

Sistema de customização pré-partida que permite aos jogadores modificar aspectos específicos de seus campeões:

#### **Categorias de Sígilos**

**🔥 Sígilos de Combate**
- **Berserker**: +8% dano, -5% resistências
- **Guardian**: +15% resistências, -10% dano
- **Assassin**: +12% velocidade de movimento, -8% vida máxima
- **Mage**: +10% poder de habilidade, +15% custo de mana

**🛡️ Sígilos de Utilidade**
- **Explorer**: +20% velocidade na jungle, visão estendida
- **Support**: +25% efetividade de cura/escudo em aliados
- **Strategist**: -15% cooldown em itens ativos
- **Survivor**: +20% regeneração de vida fora de combate

**⚡ Sígilos de Mobilidade**
- **Dasher**: Habilidade de dash adicional (cooldown 45s)
- **Teleporter**: Redução de 30% no cooldown de Teleport
- **Roamer**: +15% velocidade entre lanes
- **Escape Artist**: +25% velocidade por 3s após receber dano (cooldown 20s)

#### **Sistema de Pontos de Sigilo**
- Cada jogador possui **10 pontos** para distribuir
- Sígilos custam entre 2-4 pontos dependendo do poder
- Máximo de 3 sígilos ativos por jogador
- Sígilos podem ser trocados entre partidas, mas não durante

---

## 🗺️ **DESIGN DO MAPA INICIAL**

### **Layout Geral**

O mapa inicial segue uma estrutura clássica de MOBA com inovações únicas:

#### **Estrutura Base**
- **3 Lanes**: Superior, Média, Inferior
- **Jungle**: Área central com camps neutros
- **Bases**: Duas bases opostas com Nexus
- **Torres**: 3 torres por lane

#### **Elementos Únicos**

**Prismal Flow Core (Centro)**
- Núcleo energético no centro exato do mapa
- Área circular de 800 unidades de raio
- Terreno elevado com múltiplos acessos
- Visual: Cristal flutuante com energia pulsante

**Trilhos Energéticos**
- Três linhas de energia atravessando o mapa
- Conectam bases às áreas estratégicas
- Visualmente representados por partículas fluindo
- Ativam baseado no controle territorial

**Pontos de Controle**
- 5 pontos estratégicos que influenciam os trilhos
- Localizados em: Jungle superior, Centro, Jungle inferior, Proximidade das bases
- Controlados através de presença de equipe (similar a Dominion)

### **Jungle e Objetivos Neutros**

#### **Camps da Jungle**

**Camps Principais**
- **Ancient Golem** (Blue Buff): +20% regeneração de mana, -10% cooldown de habilidades
- **Elder Lizard** (Red Buff): Ataques aplicam burn e slow
- **Prismal Wraith**: Concede controle temporário de um trilho aleatório
- **Crystal Wolves**: Gold e XP aumentados, spawnam em packs de 3

**Camps Menores**
- **Energy Sprites**: Pequenos camps que concedem energia para trilhos
- **Mana Crystals**: Restauração instantânea de mana
- **Health Fruits**: Cura ao longo do tempo

#### **Objetivos Épicos**

**Prismal Dragon (10+ min)**
- **Localização**: Pit inferior próximo ao rio
- **Recompensa**: Buff de equipe que amplifica efeitos dos trilhos em 50%
- **Duração do Buff**: 180 segundos
- **Respawn**: 6 minutos

**Nexus Guardian (20+ min)**
- **Localização**: Pit superior próximo ao rio
- **Recompensa**: Buff que permite ativar qualquer trilho por 60 segundos
- **Duração do Buff**: Até ser usado ou 300 segundos
- **Respawn**: 7 minutos

**Prismal Lord (35+ min)**
- **Localização**: Substitui o Prismal Dragon após 35 minutos
- **Recompensa**: Todos os trilhos ficam ativos por 180 segundos + buff de dano a estruturas
- **Respawn**: 8 minutos

### **Selva Adaptativa com IA**

A jungle possui um sistema de **IA adaptativa** que aprende e responde aos padrões dos jogadores:

#### **Adaptive Spawn System**
- **Pattern Recognition**: Analisa rotas de clear da jungle
- **Dynamic Difficulty**: Ajusta dificuldade baseada na performance
- **Predictive Spawning**: Antecipa rotas baseadas no histórico
- **Counter-Strategy**: Cria contra-estratégias para padrões repetitivos

#### **Objetivos Procedurais**

O sistema gera dinamicamente objetivos secundários:

**Nexus Fragments**
- Spawnam aleatoriamente na jungle
- Concedem gold e XP bônus
- Localização baseada no estado do jogo

**Temporal Rifts**
- Portais temporários entre áreas do mapa
- Duração: 60 segundos
- Cooldown: 180-300 segundos (variável)

**Fusion Catalysts**
- Potencializam próximo camp da jungle capturado
- Efeito: Dobra recompensas por 120 segundos

---

## 🎨 **DIREÇÃO VISUAL E ARTE**

### **Estilo Visual Geral**

**Paleta de Cores Principal**
- **Base**: Tons terrosos (marrons, verdes naturais, cinzas rochosos)
- **Energia**: Azuis cristalinos, dourados solares, violetas lunares
- **Acentos**: Prateados metálicos, brancos puros para contraste

**Estilo Artístico**
- **Realismo Estilizado**: Detalhes realistas com proporções ligeiramente exageradas
- **Fantasy Medieval**: Arquitetura inspirada em castelos e ruínas antigas
- **Tecnologia Mística**: Elementos mágicos integrados naturalmente

### **Design do Mapa**

#### **Terreno Base**

**Texturas e Materiais**
- **Lanes**: Pedra trabalhada com runas incrustadas
- **Jungle**: Terra natural com vegetação densa
- **Rio**: Água cristalina com reflexos dinâmicos
- **Bases**: Arquitetura de pedra com detalhes metálicos

**Iluminação**
- **Luz Principal**: Sol posicionado a 45° criando sombras definidas
- **Luz Ambiente**: Azul suave para áreas sombreadas
- **Luzes Pontuais**: Cristais energéticos emitindo luz colorida
- **Dinâmica**: Intensidade varia conforme estado do Prismal Flow

#### **Trilhos Energéticos - Design Visual**

**Trilho Solar**
- **Cor**: Dourado brilhante com acentos laranja
- **Partículas**: Fagulhas douradas fluindo em direção aos objetivos
- **Efeito no Terreno**: Pedras brilham com energia solar
- **Som**: Zumbido energético grave com notas harmônicas

**Trilho Axis**
- **Cor**: Azul-prateado com reflexos cristalinos
- **Partículas**: Energia fluindo em padrões geométricos
- **Efeito no Terreno**: Runas antigas se iluminam
- **Som**: Tom equilibrado, nem grave nem agudo

**Trilho Lunar**
- **Cor**: Violeta com acentos prateados
- **Partículas**: Energia fluindo em ondas suaves
- **Efeito no Terreno**: Cristais lunares emergem do solo
- **Som**: Tom agudo etéreo com reverb

#### **Prismal Flow - Design Central**

**Estados Visuais**
- **Dormant**: Cristal opaco com pulso fraco
- **Awakening**: Brilho crescente com partículas orbitando
- **Active**: Energia intensa com raios conectando trilhos
- **Critical**: Explosão de luz com efeitos em todo o mapa

**Efeitos Dinâmicos**
- **Transições de Estado**: Animações de 3-5 segundos
- **Resposta a Ações**: Pulsos quando objetivos são capturados
- **Indicadores Visuais**: Cores indicam qual trilho será ativado

### **Evolução Dinâmica do Mapa**

#### **Efeitos de Transição de Fase**

**Early Game (0-10 min)**
- Mapa com cores naturais
- Trilhos dormentes (apenas estruturas visíveis)
- Prismal Flow com brilho mínimo

**Mid Game (10-25 min)**
- Cores mais vibrantes
- Trilhos começam a mostrar energia
- Prismal Flow pulsa regularmente

**Late Game (25+ min)**
- Saturação máxima de cores
- Trilhos com energia intensa
- Prismal Flow irradiando poder

#### **Narrativa Ambiental**

**Elementos de Lore Integrados**
- **Ruínas Antigas**: Estruturas que contam a história do reino
- **Cristais Energéticos**: Fontes de poder espalhadas pelo mapa
- **Símbolos Místicos**: Runas que se ativam com os trilhos
- **Vegetação Mágica**: Plantas que respondem à energia dos trilhos

### **Indicadores Visuais Estratégicos**

#### **Controle Territorial**
- **Áreas Controladas**: Sutil mudança de cor no terreno
- **Pontos de Controle**: Totens que mudam de cor por equipe
- **Influência de Trilho**: Partículas indicam direção do fluxo

#### **Estados de Nós de Recursos**
- **Disponível**: Brilho suave
- **Em Combate**: Pulsação rápida
- **Capturado**: Cor da equipe + efeito de posse
- **Cooldown**: Gradualmente retorna ao estado disponível

#### **Zonas de Perigo**
- **Áreas de Risco**: Sutil escurecimento das bordas
- **Objetivos Épicos**: Aura intimidante
- **Emboscadas Potenciais**: Sombras mais densas
- **Contagem regressiva audiovisual para perigos cronometrados

### **Otimização de Performance Visual Acessível**

#### **Estratégia LOD Adaptativa por Hardware**

**Dispositivos Entry (2GB RAM, GPU básica):**
- **Próximo (0-50m)**: Geometria simplificada, partículas mínimas
- **Médio (50-150m)**: Geometria muito básica, sem partículas
- **Distante (150m+)**: Sprites 2D, sem detalhes

**Dispositivos Mid-range (3GB RAM, GPU intermediária):**
- **Próximo (0-75m)**: Geometria moderada, partículas reduzidas
- **Médio (75-200m)**: Geometria simplificada, partículas ocasionais
- **Distante (200m+)**: Geometria básica, sem partículas

**Dispositivos High-end (4GB+ RAM, GPU avançada):**
- **Próximo (0-100m)**: Detalhes completos, todas as partículas
- **Médio (100-300m)**: Partículas reduzidas, shaders simplificados
- **Distante (300m+)**: Geometria básica, partículas mínimas

#### **Orçamentos de Partículas Escaláveis**

**Entry Level:**
- **Trilhos**: 100 partículas por seção (apenas trilho ativo)
- **Fluxo Prismal**: 300 partículas por tela
- **Ambiental**: 200 partículas total
- **Efeitos de Combate**: 500 partículas máximo

**Mid-range:**
- **Trilhos**: 250 partículas por seção (2 trilhos máximo)
- **Fluxo Prismal**: 800 partículas por tela
- **Ambiental**: 500 partículas total
- **Efeitos de Combate**: 1500 partículas máximo

**High-end:**
- **Trilhos**: 500 partículas por seção (todos os trilhos)
- **Fluxo Prismal**: 2000 partículas por tela
- **Ambiental**: 1000 partículas total
- **Efeitos de Combate**: 3000 partículas máximo

#### **Sistema de Streaming Inteligente**
- **Preloading Preditivo**: Carrega apenas áreas próximas prováveis
- **Unloading Agressivo**: Remove assets não utilizados rapidamente
- **Compressão Adaptativa**: Diferentes níveis de compressão por hardware
- **Fallback 2D**: Modo 2D completo para dispositivos muito limitados

#### **Modos de Acessibilidade**

**Modo Performance (Entry devices):**
- **Efeitos Simplificados**: Apenas 1 trilho ativo por vez
- **Trilhos Básicos**: Apenas indicadores visuais simples
- **Efeitos Mínimos**: Sem partículas decorativas
- **UI Simplificada**: Interface otimizada para telas pequenas

**Modo Balanceado (Mid-range):**
- **2 Trilhos Simultâneos**: Transições mais rápidas
- **Trilhos Moderados**: Efeitos reduzidos mas visíveis
- **Efeitos Seletivos**: Apenas efeitos importantes para gameplay
- **UI Adaptativa**: Interface que se ajusta ao tamanho da tela

**Modo Qualidade (High-end):**
- **Todos os Trilhos**: Experiência visual completa
- **Trilhos Completos**: Todos os efeitos visuais
- **Efeitos Completos**: Experiência visual máxima
- **UI Avançada**: Interface com todos os detalhes visuais

---

## 🔧 **SISTEMAS TÉCNICOS**

### **Arquitetura Core - Unreal Engine 5.6**

#### **🛠️ TECH STACK DETALHADO**

**Core Engine: Unreal Engine 5.6 - Configuração Escalável**

**Recursos Adaptativos por Hardware:**

**Entry Level (2-3GB RAM):**
- **Lumen**: Desabilitado, iluminação estática pré-calculada
- **Nanite**: Desabilitado, geometria tradicional otimizada
- **Chaos Physics**: Física simplificada, sem destruição de terreno
- **MetaHuman**: Personagens simplificados com animações básicas
- **World Partition**: Streaming básico com chunks maiores
- **Rendering**: Forward rendering, sem ray tracing

**Mid-Range (3-4GB RAM):**
- **Lumen**: Lumen simplificado apenas para áreas principais
- **Nanite**: Nanite seletivo para objetos principais
- **Chaos Physics**: Física moderada com destruição limitada
- **MetaHuman**: Personagens com qualidade média
- **World Partition**: Streaming otimizado com preloading
- **Rendering**: Deferred rendering básico, TSR habilitado

**High-End (4GB+ RAM):**
- **Lumen**: Sistema completo de iluminação global dinâmica
- **Nanite**: Geometria virtualizada completa
- **Chaos Physics**: Sistema completo de física e destruição
- **MetaHuman**: Personagens com qualidade máxima
- **World Partition**: Streaming avançado com predição
- **Rendering**: Rendering completo com ray tracing opcional

**Sistemas de Renderização Adaptativos**
- **Virtual Shadow Maps**: Habilitado apenas em hardware compatível
- **Temporal Super Resolution (TSR)**: Upscaling inteligente para dispositivos mid/high
- **Hardware Ray Tracing**: Opcional, apenas em hardware dedicado
- **Variable Rate Shading**: Otimização automática baseada na capacidade do dispositivo

#### **Arquitetura de Rede Multiplayer**

**Servidor Autoritativo com Replicação Otimizada**
- **Replicação de Objetos Dinâmicos**: Sistema de replicação para Fluxo Prismal, Trilhos e elementos dinâmicos do mapa
- **Controle de Estado de Equipe**: Replicação em tempo real do controle territorial e estados de objetivos
- **Sincronização de Transformações**: Replicação otimizada de mudanças de terreno e transições

**Sistema de Predição Client-Side**
- **Movement Prediction**: Predição de movimento para reduzir latência percebida
- **Ability Prediction**: Execução local de habilidades com validação server-side
- **Rollback Networking**: Sistema de rollback para correção de dessincronia
- **Delta Compression**: Compressão de dados de rede para reduzir bandwidth

**Validação Anti-Cheat Server-Side**
- **Validação de Ações Críticas**: Todas as ações importantes são validadas no servidor
- **Verificação de Velocidade de Movimento**: Detecção de speed hacks e movimento impossível
- **Validação de Cooldowns**: Verificação server-side de cooldowns de habilidades
- **Controle de Recursos**: Validação de consumo e geração de recursos

#### **Sistema de IA Adaptativa da Selva**

**Machine Learning Integration**
- **Coleta de Dados Comportamentais**: Sistema que monitora e armazena padrões de comportamento dos jogadores
- **Modelo de Predição**: IA que aprende com dados históricos para prever ações futuras
- **Parâmetros de Adaptação**: Sistema que ajusta dinamicamente a dificuldade e comportamento da selva
- **Processamento de Padrões**: Análise em tempo real de estratégias e rotas dos jogadores

**Adaptive Spawn System**
- **Pattern Recognition**: Análise de padrões de clear da selva por jogador
- **Dynamic Difficulty**: Ajuste automático de dificuldade baseado em performance
- **Predictive Spawning**: Antecipação de rotas de jungle baseada em histórico
- **Counter-Strategy Generation**: Criação de contra-estratégias para padrões repetitivos
- **Behavioral Learning**: Sistema que "lembra" de encontros anteriores e adapta comportamento
- **Spawn Rate Adjustment**: Modificação dinâmica de taxas de spawn baseada em análise comportamental

#### **Sistema de Geração Procedural de Objetivos**

**Procedural Objective Generator**
- **Geração Baseada no Estado do Jogo**: Sistema que analisa o estado atual da partida para gerar objetivos apropriados
- **Tipos de Objetivos Variados**: Pool de diferentes tipos de objetivos que podem ser spawned dinamicamente
- **Sistema de Pesos**: Algoritmo que determina a probabilidade de cada tipo de objetivo baseado no contexto
- **Parâmetros de Geração**: Cálculo dinâmico de parâmetros como localização, recompensas e dificuldade
- **Validação de Spawn**: Sistema que garante que objetivos são spawned em localizações estratégicas válidas

**Dynamic Balancing System**
- **Real-time Analytics**: Coleta de dados de performance em tempo real
- **Catch-up Mechanics**: Objetivos automáticos para equipes em desvantagem
- **Engagement Forcing**: Objetivos que forçam team fights quando o jogo está passivo
- **Reward Scaling**: Ajuste dinâmico de recompensas baseado no estado do jogo
- **Adaptive Timing**: Modificação de timing de objetivos baseada no ritmo da partida
- **Strategic Balancing**: Objetivos que incentivam diversidade estratégica

#### **Backend Services & Infrastructure**

**Unreal Engine Multiplayer Framework**
- **Dedicated Servers**: Servidores dedicados para partidas ranqueadas
- **Listen Servers**: Servidores P2P para partidas casuais
- **Session Management**: Gerenciamento de sessões com Epic Online Services
- **Matchmaking**: Sistema de matchmaking baseado em skill rating

**Firebase Integration**
- **Gerenciamento de Dados Persistentes**: Sistema para salvar e carregar progresso do jogador
- **Carregamento de Progresso**: Sistema assíncrono para carregar dados do jogador
- **Atualização de Estatísticas**: Sistema para atualizar estatísticas de partida em tempo real
- **Inicialização do Firebase**: Processo de setup e configuração do Firebase
- **Tratamento de Erros**: Sistema robusto de tratamento de erros de conexão e dados
- **Componente Firebase**: Interface dedicada para comunicação com serviços Firebase

**Epic Online Services (EOS)**
- **Cross-Platform Friends**: Sistema de amigos cross-platform
- **Achievements**: Sistema unificado de conquistas
- **Leaderboards**: Classificações globais e regionais
- **Voice Chat**: Integração com Vivox para comunicação por voz
- **Anti-Cheat**: EOS Anti-Cheat para detecção de trapaças

**Analytics & Telemetria**
- **Sistema de Telemetria Customizado**: Coleta detalhada de dados de gameplay e performance
- **Rastreamento de Ações**: Monitoramento de ações específicas dos jogadores com parâmetros customizados
- **Eventos de Partida**: Coleta de dados sobre eventos importantes durante as partidas
- **Métricas de Performance**: Monitoramento de framerate, latência e uso de recursos
- **Envio em Lote**: Sistema otimizado para enviar dados de telemetria em batches
- **Processamento de Dados de Balanceamento**: Análise de dados para ajustes de balanceamento
- **Eventos Pendentes**: Sistema de queue para eventos de telemetria
- **Timer de Envio**: Gerenciamento automático de timing para envio de dados

#### **Sistema de Partículas Avançado**

**Niagara Particle System Integration**
- **Gerenciador de Partículas dos Trilhos**: Sistema dedicado para gerenciar efeitos visuais dos Trilhos
- **Ativação de Efeitos**: Sistema para ativar diferentes tipos de efeitos baseados no tipo de Trilho
- **Atualização de Fluxo**: Sistema dinâmico para atualizar direção e velocidade do fluxo de partículas
- **Efeitos Específicos por Trilho**: Sistemas de partículas únicos para Solar, Axis e Lunar Trilhos
- **Componente Ativo**: Gerenciamento do componente de partículas atualmente ativo
- **Otimização de Contagem**: Sistema que ajusta número de partículas baseado em jogadores próximos
- **Ajuste de Qualidade**: Modificação automática da qualidade baseada no hardware do dispositivo

**GPU-Driven Particle Culling**
- **Frustum Culling**: Culling automático de partículas fora da visão
- **Distance-Based LOD**: Redução automática de partículas baseada na distância
- **Occlusion Culling**: Desativação de partículas ocluídas por geometria
- **Performance Budgeting**: Sistema de orçamento dinâmico de partículas
- **Adaptive Quality**: Ajuste automático de qualidade baseado na performance
- **Memory Management**: Gerenciamento inteligente de memória para sistemas de partículas

#### **Otimização de Performance Multiplataforma**

**Performance Targets Acessíveis e Escaláveis**
| **Platform** | **FPS** | **Resolution** | **Memory** | **Storage** | **CPU Threads** | **GPU Memory** |
|--------------|---------|----------------|------------|-------------|-----------------|----------------|
| **Entry Mobile** | 30 FPS | 480p-720p | <2GB RAM | <3GB | 2-4 cores | <512MB VRAM |
| **Mid-range Mobile** | 45 FPS | 720p-900p | <3GB RAM | <4GB | 4 cores | <1GB VRAM |
| **High-end Mobile** | 60 FPS | 1080p+ | <4GB RAM | <6GB | 4-8 cores | <2GB VRAM |
| **PC Entry** | 45 FPS | 900p-1080p | <6GB RAM | <8GB | 4 cores | <2GB VRAM |
| **PC Mid** | 60 FPS | 1080p | <8GB RAM | <10GB | 4-6 cores | <4GB VRAM |
| **PC High** | 90 FPS | 1440p+ | <12GB RAM | <15GB | 6+ cores | <6GB VRAM |

**Sistema de Qualidade Adaptativa e Acessível**

**Configurações Escaláveis por Nível de Hardware:**

**Nível 1 - Dispositivos Entry (2GB RAM, GPU básica)**
- **Partículas**: Densidade mínima (25% do máximo), efeitos simplificados
- **Sombras**: Sombras básicas apenas para jogadores, sem sombras ambientais
- **Texturas**: 512x512 máximo, compressão agressiva
- **Efeitos**: Pós-processamento desabilitado, bloom simplificado
- **Trilhos**: Apenas 1 trilho visível por vez, efeitos reduzidos
- **Lumen**: Desabilitado, iluminação estática pré-calculada

**Nível 2 - Dispositivos Mid-range (3GB RAM, GPU intermediária)**
- **Partículas**: Densidade média (50% do máximo), efeitos moderados
- **Sombras**: Sombras dinâmicas para jogadores e objetivos principais
- **Texturas**: 1024x1024, compressão moderada
- **Efeitos**: Pós-processamento básico, anti-aliasing FXAA
- **Trilhos**: 2 trilhos simultâneos, efeitos reduzidos
- **Lumen**: Lumen simplificado apenas para áreas principais

**Nível 3 - Dispositivos High-end (4GB+ RAM, GPU avançada)**
- **Partículas**: Densidade alta (75-100% do máximo), efeitos completos
- **Sombras**: Sombras dinâmicas completas, cascaded shadow maps
- **Texturas**: 2048x2048+, compressão mínima
- **Efeitos**: Pós-processamento completo, TAA/TSR
- **Trilhos**: Todos os trilhos simultâneos, efeitos completos
- **Lumen**: Lumen completo com reflexões dinâmicas

**Sistema de Detecção Automática Inteligente**
- **Benchmark Rápido**: Teste de 5 segundos para classificar dispositivo
- **Detecção de Hardware**: Identificação automática de GPU, RAM e CPU
- **Ajuste Progressivo**: Sistema que aumenta qualidade gradualmente se performance permitir
- **Fallback Inteligente**: Redução automática de qualidade se FPS cair abaixo do target
- **Configuração Manual**: Opção para usuários avançados ajustarem manualmente

**Memory Management System**
- **Gerenciador de Memória Otimizado**: Sistema dedicado para otimização de uso de memória
- **Pré-carregamento de Assets Críticos**: Carregamento antecipado de recursos essenciais
- **Descarregamento de Assets**: Remoção inteligente de recursos não utilizados
- **Orçamento de Memória**: Sistema de definição de limites de memória por categoria
- **Monitoramento de Uso**: Acompanhamento contínuo do uso de memória
- **Garbage Collection**: Acionamento inteligente de limpeza de memória
- **Streaming Preditivo**: Carregamento de assets baseado em predições de uso

**Memory Budget Categories**
- **Memória de Texturas**: Orçamento dedicado para texturas e materiais
- **Memória de Áudio**: Limite para arquivos de som e música
- **Memória de Meshes**: Orçamento para geometria e modelos 3D
- **Memória de Partículas**: Limite para sistemas de partículas
- **Assets Pré-carregados**: Lista de recursos mantidos em memória
- **Timer de Monitoramento**: Sistema de verificação periódica de memória

#### **Arquitetura de Rede Avançada**

**Servidor Autoritativo com Otimizações**
- **Interest Management**: Replicação baseada em relevância espacial
- **Priority-Based Replication**: Priorização de objetos críticos para gameplay
- **Bandwidth Optimization**: Compressão adaptativa baseada na qualidade da conexão
- **Lag Compensation**: Compensação de latência para ações críticas

**Network Prediction System**
- **Sistema de Predição Avançado**: Framework para reduzir latência percebida
- **Predição de Movimento**: Antecipação de movimento baseada em input do jogador
- **Predição de Habilidades**: Execução local de habilidades com validação posterior
- **Correção Server-Side**: Sistema de correção quando predições divergem do servidor
- **Estado de Predição**: Estrutura que armazena posição, rotação, velocidade e timing
- **Histórico de Predições**: Buffer de estados anteriores para rollback
- **Rollback de Frames**: Sistema para voltar a estados anteriores quando necessário
- **Replay de Inputs**: Re-execução de inputs após correções do servidor
- **Validação de Predições**: Comparação entre estados cliente e servidor

**Anti-Cheat Integration**
- **Sistema Anti-Cheat Integrado**: Framework completo para detecção e prevenção de trapaças
- **Inicialização**: Setup e configuração do sistema anti-cheat
- **Relatório de Atividades Suspeitas**: Sistema para reportar diferentes tipos de trapaças
- **Validação de Estatísticas**: Verificação de estatísticas de jogador para detectar anomalias
- **Verificação de Velocidade**: Monitoramento de velocidade de movimento para detectar speed hacks
- **Validação de Habilidades**: Verificação de uso correto de habilidades e cooldowns
- **Monitoramento de Padrões**: Análise de padrões de input para detectar bots

**Cheat Detection Data**
- **Velocidades de Movimento**: Histórico de velocidades para análise
- **Cooldowns de Habilidades**: Rastreamento de uso de habilidades
- **Histórico de Posições**: Tracking de posições para validação de movimento
- **Nível de Suspeita**: Score acumulativo de atividades suspeitas
- **Dados por Jogador**: Mapeamento de dados de detecção por jogador
- **EOS Anti-Cheat**: Integração com Epic Online Services Anti-Cheat

#### **Cross-Platform Integration**

**Platform-Specific Optimizations**
- **Otimizador de Plataforma**: Sistema para aplicar otimizações específicas por plataforma
- **Aplicação de Otimizações**: Framework para aplicar configurações otimizadas automaticamente
- **Otimização Mobile**: Configurações específicas para dispositivos móveis
- **Otimização PC**: Configurações para computadores desktop
- **Otimização Console**: Preparação para futuras versões de console

**Mobile Optimizations**
- **Configuração de Renderer**: Ajustes específicos para GPUs móveis
- **Setup de Input**: Configuração de controles touch otimizados
- **Ajuste de UI**: Interface adaptada para telas menores

**PC Optimizations**
- **Habilitação de Features**: Ativação de recursos avançados disponíveis em PC
- **Configuração de Input**: Setup para mouse, teclado e controles
- **Setup Gráfico**: Configurações gráficas avançadas para hardware PC

**Epic Online Services Integration**
- **Cross-Platform Friends**: Sistema unificado de amigos
- **Cross-Platform Matchmaking**: Matchmaking entre plataformas
- **Cross-Platform Voice Chat**: Comunicação por voz multiplataforma
- **Cross-Platform Progression**: Progressão sincronizada entre dispositivos

---

## 💰 **PROGRESSÃO E MONETIZAÇÃO**

### **Modelo de Monetização Ética**

#### **Battle Pass Evoluído**

**🎁 ADAPTIVE BATTLE PASS:**
- **Traditional Track**: Progressão linear padrão
- **Role-Specific Tracks**: Trilhas específicas por função (Tank, DPS, Support, Jungle, Mid)
- **Playstyle Tracks**: Trilhas por estilo de jogo (Agressivo, Defensivo, Estratégico)
- **Community Tracks**: Desbloqueadas através de objetivos comunitários

**Exemplo de Funcionamento:**
Jogador que atua principalmente como Support desbloqueia a Support Track:
- Skins exclusivas para campeões de suporte
- Customizações de Baliza
- Variações de VFX para cura e escudo
- Emotes e voice lines específicos de suporte

#### **Champion Acquisition Inclusivo**
- **Free Rotation**: 20 champions/week (vs 10 do Wild Rift) + rotação de "Community Favorites"
- **Earn Rate**: 1 novo champion/semana jogando casual + bônus por comportamento positivo
- **Currency**: Blue Essence (earned) + Realm Crystals (premium) + Harmony Tokens (comportamento positivo)
- **No P2W**: Champions purchasable apenas com earned currency
- **Community Unlock**: Comunidade pode desbloquear champions para todos através de eventos colaborativos
- **Mentorship Bonus**: Mentores ganham champions mais rápido
- **Accessibility Fund**: Champions gratuitos para jogadores com dificuldades financeiras

#### **Cosmetics Premium**

**✨ FUNCTIONAL COSMETICS:**

**Champion Skins**
- Alterações de modelo
- Customização de VFX
- Variações de voice pack
- Alterações na aparência dos Sígilos

**Map Themes (Votação Comunitária)**
- Aparências sazonais do mapa
- Efeitos climáticos
- Pacotes de som ambiente

**Elementos Customizáveis**
- Cores de partículas de habilidades
- Animações de recall
- Celebrações de vitória/derrota

### **Progression Systems**

#### **Account Level (1-500)**

**MARCOS DE PROGRESSÃO:**
- **Level 10**: Desbloqueio do modo ranqueado
- **Level 25**: Desbloqueio dos Sígilos Auracron
- **Level 50**: Rastreamento de maestria de mapa
- **Level 100**: Criação de lobbies customizados
- **Level 200**: Privilégios de beta tester
- **Level 500**: Status lendário + recompensas únicas

#### **Champion Mastery (1-10)**

**PROGRESSÃO POR CAMPEÃO:**
- **Maestria 1-3**: Recompensas cosméticas básicas
- **Maestria 4-6**: Chromas avançados de skin
- **Maestria 7-8**: Emotes e animações exclusivos
- **Maestria 9-10**: Título de campeão + borda
- **Maestria 10**: Nomes customizados de habilidades + recompensas raras

#### **Map Mastery (Sistema Novo)**

**PROGRESSÃO ESPECÍFICA DO MAPA:**

**Trilho Solar - Expertise em Combate Ofensivo**
- Bônus de XP na fase de lanes
- Eficiência no uso de buffs ofensivos
- Prioridade em objetivos ofensivos

**Trilho Axis - Maestria em Controle Central**
- Bônus de controle na área central
- Consciência de posicionamento estratégico
- Sinergia com objetivos centrais

**Trilho Lunar - Expertise em Suporte e Defesa**
- Raio de detecção de perigos
- Velocidade de navegação defensiva
- Multiplicador de efetividade de cura

**Nota importante**: Toda documentação futura deve utilizar a nomenclatura "Sígilos Auracron" ao invés de "Champion Fusion" e seguir a terminologia padronizada estabelecida neste documento.

---

## 📝 **NOTAS DE DESENVOLVIMENTO**

### **Versão Inicial - Foco no Mapa Único**

Esta versão do documento foi criada especificamente para o desenvolvimento inicial do AURACRON, focando em um único mapa para facilitar o desenvolvimento e testes. As mecânicas principais foram mantidas, mas simplificadas para funcionar em um ambiente de mapa único:

- **Trilhos Dinâmicos**: Mantidos os três trilhos (Solar, Axis, Lunar) funcionando no mapa principal
- **Prismal Flow**: Sistema central preservado com todos os estados
- **Sígilos Auracron**: Sistema completo de customização mantido
- **IA Adaptativa**: Funcionalidades preservadas para o mapa único
- **Objetivos Procedurais**: Adaptados para funcionar no mapa principal

### **Próximas Iterações**

Quando o desenvolvimento avançar, poderemos expandir para:
- Múltiplos mapas com diferentes layouts
- Sistemas de transição entre realms
- Mecânicas específicas por tipo de mapa
- Modos de jogo alternativos

Esta abordagem permite um desenvolvimento mais focado e iterativo, garantindo que as mecânicas core estejam sólidas antes da expansão para sistemas mais complexos.