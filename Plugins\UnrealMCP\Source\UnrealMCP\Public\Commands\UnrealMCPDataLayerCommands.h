// Copyright Epic Games, Inc. All Rights Reserved.
// UnrealMCP Data Layer Commands - Unreal Engine 5.6 Compatible
// Robust implementation for Data Layer management with modern UE 5.6 APIs

#pragma once

#include "CoreMinimal.h"
#include "Json.h"

/**
 * Handler class for Data Layer-related MCP commands
 * Handles Data Layer creation, management, and organization
 */
class UNREALMCP_API FUnrealMCPDataLayerCommands
{
public:
    FUnrealMCPDataLayerCommands();

    // Handle data layer commands
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:

    // Data Layer Asset Management
    TSharedPtr<FJsonObject> HandleCreateDataLayerAsset(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleCreateDataLayerAssetsBatch(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleCreateDataLayerInstance(const TSharedPtr<FJsonObject>& Params);

    // Actor Management in Data Layers
    TSharedPtr<FJsonObject> HandleAddActorsToDataLayer(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleRemoveActorsFromDataLayer(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleOrganizeActorsIntoDataLayers(const TSharedPtr<FJsonObject>& Params);

    // Data Layer State Management
    TSharedPtr<FJsonObject> HandleSetDataLayerVisibility(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleSetDataLayerRuntimeState(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleLoadDataLayer(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleUnloadDataLayer(const TSharedPtr<FJsonObject>& Params);

    // Query and Information Functions
    TSharedPtr<FJsonObject> HandleGetAllDataLayers(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleGetActorsInDataLayer(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleGetActorDataLayers(const TSharedPtr<FJsonObject>& Params);

private:
    // Helper functions
    UWorld* GetValidWorld();
};
