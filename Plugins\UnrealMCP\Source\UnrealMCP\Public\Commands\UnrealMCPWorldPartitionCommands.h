#pragma once

#include "CoreMinimal.h"
#include "Json.h"

/**
 * Handler class for World Partition-related MCP commands
 * Handles World Partition configuration, streaming, and HLOD management
 */
class UNREALMCP_API FUnrealMCPWorldPartitionCommands
{
public:
    FUnrealMCPWorldPartitionCommands();

    // Handle world partition commands
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // World Partition Configuration
    TSharedPtr<FJsonObject> HandleEnableWorldPartition(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleConfigureWorldPartitionGrid(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleSetStreamingPolicy(const TSharedPtr<FJsonObject>& Params);

    // Streaming Management
    TSharedPtr<FJsonObject> HandleConfigureStreamingDistances(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleAddStreamingSource(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleRemoveStreamingSource(const TSharedPtr<FJsonObject>& Params);

    // HLOD Management
    TSharedPtr<FJsonObject> HandleConfigureHLOD(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleBuildHLOD(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleCreateHLODLayer(const TSharedPtr<FJsonObject>& Params);

    // Performance Optimization
    TSharedPtr<FJsonObject> HandleOptimizeForLargeWorld(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleGetPerformanceMetrics(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleSetPerformanceBudgets(const TSharedPtr<FJsonObject>& Params);

    // Query and Information Functions
    TSharedPtr<FJsonObject> HandleGetWorldPartitionInfo(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleGetLoadedCells(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleGetStreamingSources(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleValidateConfiguration(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleValidateWPConfiguration(const TSharedPtr<FJsonObject>& Params);

    // Utility Functions
    UWorld* GetValidWorld();
};
