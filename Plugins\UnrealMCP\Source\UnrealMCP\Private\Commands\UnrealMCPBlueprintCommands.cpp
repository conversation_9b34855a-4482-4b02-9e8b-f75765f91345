// === INCLUDES CORRIGIDOS PARA UE5.6 ===
#include "Commands/UnrealMCPBlueprintCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Core Blueprint System
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Factories/BlueprintFactory.h"

// Blueprint Editor Utils (Caminhos Verificados UE5.6)
#include "Kismet2/BlueprintEditorUtils.h"
#include "Kismet2/KismetEditorUtilities.h"

// Blueprint Graph Nodes
#include "EdGraphSchema_K2.h"
#include "K2Node_Event.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"

// Construction Script System
#include "Engine/SimpleConstructionScript.h"
#include "Engine/SCS_Node.h"

// Component System (Caminhos Verificados UE5.6)
#include "Components/ActorComponent.h"
#include "Components/SceneComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "GameFramework/SpringArmComponent.h"

// Property System UE5.6 (Headers Modernizados)


#include "UObject/UObjectGlobals.h"
#include "UObject/PropertyIterator.h"

// Asset Management
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "UObject/Package.h"
#include "UObject/SavePackage.h"

// Framework Classes
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"

// Logging Category Declaration
DECLARE_LOG_CATEGORY_EXTERN(LogUnrealMCP, Log, All);
DEFINE_LOG_CATEGORY(LogUnrealMCP);

FUnrealMCPBlueprintCommands::FUnrealMCPBlueprintCommands()
    : bCacheInitialized(false)
{
    InitializeClassCache();
}

// Validação robusta de parâmetros conforme auditoria UE5.6
TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params, const TArray<FString>& RequiredFields)
{
    if (!Params.IsValid())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid or null parameters object"));
    }
    
    for (const FString& Field : RequiredFields)
    {
        if (!Params->HasField(Field))
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(
                FString::Printf(TEXT("Missing required parameter: %s"), *Field)
            );
        }
        
        const FString FieldValue = Params->GetStringField(Field);
        if (FieldValue.IsEmpty())
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(
                FString::Printf(TEXT("Empty required parameter: %s"), *Field)
            );
        }
    }
    
    return nullptr; // Sucesso - sem erro
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandType == TEXT("create_blueprint"))
    {
        return HandleCreateBlueprint(Params);
    }
    else if (CommandType == TEXT("add_component_to_blueprint"))
    {
        return HandleAddComponentToBlueprint(Params);
    }
    else if (CommandType == TEXT("set_component_property"))
    {
        return HandleSetComponentProperty(Params);
    }
    else if (CommandType == TEXT("set_physics_properties"))
    {
        return HandleSetPhysicsProperties(Params);
    }
    else if (CommandType == TEXT("compile_blueprint"))
    {
        return HandleCompileBlueprint(Params);
    }
    else if (CommandType == TEXT("spawn_blueprint_actor"))
    {
        return HandleSpawnBlueprintActor(Params);
    }
    else if (CommandType == TEXT("set_blueprint_property"))
    {
        return HandleSetBlueprintProperty(Params);
    }
    else if (CommandType == TEXT("set_static_mesh_properties"))
    {
        return HandleSetStaticMeshProperties(Params);
    }
    else if (CommandType == TEXT("set_pawn_properties"))
    {
        return HandleSetPawnProperties(Params);
    }
    else if (CommandType == TEXT("create_blueprint_interface"))
    {
        return HandleCreateBlueprintInterface(Params);
    }
    else if (CommandType == TEXT("add_blueprint_function"))
    {
        return HandleAddBlueprintFunction(Params);
    }
    else if (CommandType == TEXT("add_blueprint_variable"))
    {
        return HandleAddBlueprintVariable(Params);
    }
    else if (CommandType == TEXT("get_blueprint_info"))
    {
        return HandleGetBlueprintInfo(Params);
    }
    else if (CommandType == TEXT("delete_blueprint"))
    {
        return HandleDeleteBlueprint(Params);
    }
    else if (CommandType == TEXT("duplicate_blueprint"))
    {
        return HandleDuplicateBlueprint(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown blueprint command: %s"), *CommandType));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleCreateBlueprint(const TSharedPtr<FJsonObject>& Params)
{
    // Validação robusta de parâmetros com const correctness
    const TArray<FString> RequiredFields = {TEXT("name")};
    if (const auto ValidationError = ValidateRequiredParams(Params, RequiredFields))
    {
        return ValidationError;
    }
    
    // Get required parameters with const correctness
    const FString BlueprintName = Params->GetStringField(TEXT("name"));

    // Check if blueprint already exists
    const FString PackagePath = TEXT("/Game/Blueprints/");
    const FString AssetName = BlueprintName;
    if (UEditorAssetLibrary::DoesAssetExist(PackagePath + AssetName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint already exists: %s"), *BlueprintName));
    }

    // Create the blueprint factory with proper object management
    UBlueprintFactory* const Factory = NewObject<UBlueprintFactory>();
    if (!ensure(Factory))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create blueprint factory"));
    }
    
    // Handle parent class with const correctness
    FString ParentClass;
    Params->TryGetStringField(TEXT("parent_class"), ParentClass);
    
    // Default to Actor if no parent class specified
    const UClass* SelectedParentClass = AActor::StaticClass();
    
    // Try to find the specified parent class using optimized cache
    if (!ParentClass.IsEmpty())
    {
        if (const UClass* FoundClass = GetCachedClass(ParentClass))
        {
            SelectedParentClass = FoundClass;
            UE_LOG(LogUnrealMCP, Log, TEXT("Successfully set parent class to '%s'"), *ParentClass);
        }
        else
        {
            UE_LOG(LogUnrealMCP, Warning, TEXT("Could not find specified parent class '%s', defaulting to AActor"), *ParentClass);
        }
    }
    
    Factory->ParentClass = const_cast<UClass*>(SelectedParentClass);

    // Create the blueprint
    UPackage* Package = CreatePackage(*(PackagePath + AssetName));
    UBlueprint* NewBlueprint = Cast<UBlueprint>(Factory->FactoryCreateNew(UBlueprint::StaticClass(), Package, *AssetName, RF_Standalone | RF_Public, nullptr, GWarn));

    if (NewBlueprint)
    {
        // Notify the asset registry
        FAssetRegistryModule::AssetCreated(NewBlueprint);

        // Mark the package dirty
        Package->MarkPackageDirty();
        
        // Save the asset to disk to ensure physical file creation
        const FString PackageFileName = FPackageName::LongPackageNameToFilename(Package->GetName(), FPackageName::GetAssetPackageExtension());
        FSavePackageArgs SaveArgs;
        SaveArgs.TopLevelFlags = RF_Standalone;
        SaveArgs.Error = GError;
        SaveArgs.bSlowTask = true;
        SaveArgs.bWarnOfLongFilename = true;
        if (UPackage::SavePackage(Package, NewBlueprint, *PackageFileName, SaveArgs))
        {
            UE_LOG(LogUnrealMCP, Log, TEXT("Successfully saved blueprint '%s' to disk at '%s'"), *AssetName, *PackageFileName);
        }
        else
        {
            UE_LOG(LogUnrealMCP, Warning, TEXT("Failed to save blueprint '%s' to disk"), *AssetName);
        }

        TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
        ResultObj->SetStringField(TEXT("name"), AssetName);
        ResultObj->SetStringField(TEXT("path"), PackagePath + AssetName);
        return ResultObj;
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create blueprint"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleAddComponentToBlueprint(const TSharedPtr<FJsonObject>& Params)
{
    // Validação robusta de parâmetros com const correctness
    const TArray<FString> RequiredFields = {TEXT("blueprint_name"), TEXT("component_type"), TEXT("component_name")};
    if (const auto ValidationError = ValidateRequiredParams(Params, RequiredFields))
    {
        return ValidationError;
    }
    
    // Get required parameters with const correctness
    const FString BlueprintName = Params->GetStringField(TEXT("blueprint_name"));
    const FString ComponentType = Params->GetStringField(TEXT("component_type"));
    const FString ComponentName = Params->GetStringField(TEXT("component_name"));

    // Find the blueprint com proteção robusta
    UBlueprint* const Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!ensure(Blueprint))
    {
        UE_LOG(LogUnrealMCP, Error, TEXT("Blueprint not found: %s"), *BlueprintName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }
    
    // Verificação adicional de integridade do Blueprint
    if (!ensure(Blueprint->SimpleConstructionScript))
    {
        UE_LOG(LogUnrealMCP, Error, TEXT("Blueprint SimpleConstructionScript is null for: %s"), *BlueprintName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Blueprint construction script is invalid"));
    }

    // Create the component using optimized cache lookup with const correctness
    const UClass* ComponentClass = GetCachedComponentClass(ComponentType);
    
    // Verify that the class is a valid component type com proteção robusta
    if (!ensure(ComponentClass))
    {
        UE_LOG(LogUnrealMCP, Error, TEXT("Failed to load component class: %s"), *ComponentType);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown component type: %s"), *ComponentType));
    }
    
    if (!ensure(ComponentClass->IsChildOf(UActorComponent::StaticClass())))
    {
        UE_LOG(LogUnrealMCP, Error, TEXT("Class %s is not a valid ActorComponent type"), *ComponentType);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Invalid component type: %s"), *ComponentType));
    }

    // Add the component to the blueprint com verificação de integridade
    check(Blueprint->SimpleConstructionScript); // Já verificado anteriormente, mas garantindo
    USCS_Node* const NewNode = Blueprint->SimpleConstructionScript->CreateNode(const_cast<UClass*>(ComponentClass), *ComponentName);
    
    if (!ensure(NewNode))
    {
        UE_LOG(LogUnrealMCP, Error, TEXT("Failed to create SCS node for component: %s"), *ComponentName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create component node"));
    }
    if (ensure(NewNode))
    {
        // Set transform if provided with const correctness
        if (USceneComponent* const SceneComponent = Cast<USceneComponent>(NewNode->ComponentTemplate))
        {
            if (Params->HasField(TEXT("location")))
            {
                const FVector Location = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("location"));
                SceneComponent->SetRelativeLocation(Location);
            }
            if (Params->HasField(TEXT("rotation")))
            {
                const FRotator Rotation = FUnrealMCPCommonUtils::GetRotatorFromJson(Params, TEXT("rotation"));
                SceneComponent->SetRelativeRotation(Rotation);
            }
            if (Params->HasField(TEXT("scale")))
            {
                const FVector Scale = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("scale"));
                SceneComponent->SetRelativeScale3D(Scale);
            }
        }

        // Add to root if no parent specified
        Blueprint->SimpleConstructionScript->AddNode(NewNode);

        // Compile the blueprint
        FKismetEditorUtilities::CompileBlueprint(Blueprint);
        
        // Save the blueprint to disk to ensure physical file persistence
        UPackage* const BlueprintPackage = Blueprint->GetPackage();
        if (ensure(BlueprintPackage))
        {
            BlueprintPackage->MarkPackageDirty();
            const FString PackageFileName = FPackageName::LongPackageNameToFilename(BlueprintPackage->GetName(), FPackageName::GetAssetPackageExtension());
            FSavePackageArgs SaveArgs;
            SaveArgs.TopLevelFlags = RF_Standalone;
            SaveArgs.Error = GError;
            SaveArgs.bSlowTask = true;
            SaveArgs.bWarnOfLongFilename = true;
            if (UPackage::SavePackage(BlueprintPackage, Blueprint, *PackageFileName, SaveArgs))
            {
                UE_LOG(LogUnrealMCP, Log, TEXT("Successfully saved blueprint '%s' with component '%s' to disk"), *BlueprintName, *ComponentName);
            }
            else
            {
                UE_LOG(LogUnrealMCP, Warning, TEXT("Failed to save blueprint '%s' with component '%s' to disk"), *BlueprintName, *ComponentName);
            }
        }

        TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
        ResultObj->SetStringField(TEXT("component_name"), ComponentName);
        ResultObj->SetStringField(TEXT("component_type"), ComponentType);
        return ResultObj;
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to add component to blueprint"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleSetComponentProperty(const TSharedPtr<FJsonObject>& Params)
{
    // Validação robusta de parâmetros
    TArray<FString> RequiredFields = {TEXT("blueprint_name"), TEXT("component_name"), TEXT("property_name")};
    TSharedPtr<FJsonObject> ValidationError = ValidateRequiredParams(Params, RequiredFields);
    if (ValidationError.IsValid())
    {
        return ValidationError;
    }
    
    // Get validated parameters
    FString BlueprintName = Params->GetStringField(TEXT("blueprint_name"));
    FString ComponentName = Params->GetStringField(TEXT("component_name"));
    FString PropertyName = Params->GetStringField(TEXT("property_name"));

    // Log all input parameters for debugging
    UE_LOG(LogUnrealMCP, Log, TEXT("SetComponentProperty - Blueprint: %s, Component: %s, Property: %s"), 
        *BlueprintName, *ComponentName, *PropertyName);
    
    // Log property_value if available
    if (Params->HasField(TEXT("property_value")))
    {
        TSharedPtr<FJsonValue> JsonValue = Params->Values.FindRef(TEXT("property_value"));
        if (ensure(JsonValue.IsValid()))
        {
            FString ValueType;
            
            switch(JsonValue->Type)
            {
                case EJson::Boolean: ValueType = FString::Printf(TEXT("Boolean: %s"), JsonValue->AsBool() ? TEXT("true") : TEXT("false")); break;
                case EJson::Number: ValueType = FString::Printf(TEXT("Number: %f"), JsonValue->AsNumber()); break;
                case EJson::String: ValueType = FString::Printf(TEXT("String: %s"), *JsonValue->AsString()); break;
                case EJson::Array: ValueType = TEXT("Array"); break;
                case EJson::Object: ValueType = TEXT("Object"); break;
                default: ValueType = TEXT("Unknown"); break;
            }
            
            UE_LOG(LogUnrealMCP, Log, TEXT("SetComponentProperty - Value Type: %s"), *ValueType);
        }
    }
    else
    {
        UE_LOG(LogUnrealMCP, Warning, TEXT("SetComponentProperty - No property_value provided"));
    }

    // Find the blueprint com proteção robusta
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!ensure(Blueprint))
    {
        UE_LOG(LogUnrealMCP, Error, TEXT("SetComponentProperty - Blueprint not found: %s"), *BlueprintName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Blueprint found: %s (Class: %s)"), 
            *BlueprintName, 
            Blueprint->GeneratedClass ? *Blueprint->GeneratedClass->GetName() : TEXT("NULL"));
    }

    // Find the component
    USCS_Node* ComponentNode = nullptr;
    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Searching for component %s in blueprint nodes"), *ComponentName);
    
    if (!Blueprint->SimpleConstructionScript)
    {
        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - SimpleConstructionScript is NULL for blueprint %s"), *BlueprintName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid blueprint construction script"));
    }
    
    for (USCS_Node* Node : Blueprint->SimpleConstructionScript->GetAllNodes())
    {
        if (Node)
        {
            UE_LOG(LogTemp, Verbose, TEXT("SetComponentProperty - Found node: %s"), *Node->GetVariableName().ToString());
            if (Node->GetVariableName().ToString() == ComponentName)
            {
                ComponentNode = Node;
                break;
            }
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - Found NULL node in blueprint"));
        }
    }

    if (!ComponentNode)
    {
        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Component not found: %s"), *ComponentName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Component found: %s (Class: %s)"), 
            *ComponentName, 
            ComponentNode->ComponentTemplate ? *ComponentNode->ComponentTemplate->GetClass()->GetName() : TEXT("NULL"));
    }

    // Get the component template
    UObject* ComponentTemplate = ComponentNode->ComponentTemplate;
    if (!ComponentTemplate)
    {
        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Component template is NULL for %s"), *ComponentName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid component template"));
    }

    // Check if this is a Spring Arm component and log special debug info
    if (ComponentTemplate->GetClass()->GetName().Contains(TEXT("SpringArm")))
    {
        UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - SpringArm component detected! Class: %s"), 
            *ComponentTemplate->GetClass()->GetPathName());
            
        // Log all properties of the SpringArm component class
        UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - SpringArm properties:"));
        for (TFieldIterator<FProperty> PropIt(ComponentTemplate->GetClass()); PropIt; ++PropIt)
        {
            FProperty* Prop = *PropIt;
            UE_LOG(LogTemp, Warning, TEXT("  - %s (%s)"), *Prop->GetName(), *Prop->GetCPPType());
        }

        // Special handling for Spring Arm properties
        if (Params->HasField(TEXT("property_value")))
        {
            TSharedPtr<FJsonValue> JsonValue = Params->Values.FindRef(TEXT("property_value"));
            
            // Get the property using the new FField system
            FProperty* Property = FindFProperty<FProperty>(ComponentTemplate->GetClass(), *PropertyName);
            if (!Property)
            {
                UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Property %s not found on SpringArm component"), *PropertyName);
                return FUnrealMCPCommonUtils::CreateErrorResponse(
                    FString::Printf(TEXT("Property %s not found on SpringArm component"), *PropertyName));
            }

            // Create a scope guard to ensure property cleanup
            struct FScopeGuard
            {
                UObject* Object;
                FScopeGuard(UObject* InObject) : Object(InObject) 
                {
                    if (Object)
                    {
                        Object->Modify();
                    }
                }
                ~FScopeGuard()
                {
                    if (Object)
                    {
                        Object->PostEditChange();
                    }
                }
            } ScopeGuard(ComponentTemplate);

            bool bSuccess = false;
            FString ErrorMessage;

            // Handle specific Spring Arm property types
            if (FFloatProperty* FloatProp = CastField<FFloatProperty>(Property))
            {
                if (JsonValue->Type == EJson::Number)
                {
                    const float Value = JsonValue->AsNumber();
                    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting float property %s to %f"), *PropertyName, Value);
                    FloatProp->SetPropertyValue_InContainer(ComponentTemplate, Value);
                    bSuccess = true;
                }
            }
            else if (FBoolProperty* BoolProp = CastField<FBoolProperty>(Property))
            {
                if (JsonValue->Type == EJson::Boolean)
                {
                    const bool Value = JsonValue->AsBool();
                    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting bool property %s to %d"), *PropertyName, Value);
                    BoolProp->SetPropertyValue_InContainer(ComponentTemplate, Value);
                    bSuccess = true;
                }
            }
            else if (FStructProperty* StructProp = CastField<FStructProperty>(Property))
            {
                UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Handling struct property %s of type %s"), 
                    *PropertyName, *StructProp->Struct->GetName());
                
                // Special handling for common Spring Arm struct properties
                if (StructProp->Struct == TBaseStructure<FVector>::Get())
                {
                    if (JsonValue->Type == EJson::Array)
                    {
                        const TArray<TSharedPtr<FJsonValue>>& Arr = JsonValue->AsArray();
                        if (Arr.Num() == 3)
                        {
                            FVector Vec(
                                Arr[0]->AsNumber(),
                                Arr[1]->AsNumber(),
                                Arr[2]->AsNumber()
                            );
                            void* PropertyAddr = StructProp->ContainerPtrToValuePtr<void>(ComponentTemplate);
                            StructProp->CopySingleValue(PropertyAddr, &Vec);
                            bSuccess = true;
                        }
                    }
                }
                else if (StructProp->Struct == TBaseStructure<FRotator>::Get())
                {
                    if (JsonValue->Type == EJson::Array)
                    {
                        const TArray<TSharedPtr<FJsonValue>>& Arr = JsonValue->AsArray();
                        if (Arr.Num() == 3)
                        {
                            FRotator Rot(
                                Arr[0]->AsNumber(),
                                Arr[1]->AsNumber(),
                                Arr[2]->AsNumber()
                            );
                            void* PropertyAddr = StructProp->ContainerPtrToValuePtr<void>(ComponentTemplate);
                            StructProp->CopySingleValue(PropertyAddr, &Rot);
                            bSuccess = true;
                        }
                    }
                }
            }

            if (bSuccess)
            {
                // Mark the blueprint as modified
                UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Successfully set SpringArm property %s"), *PropertyName);
                FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

                TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
                ResultObj->SetStringField(TEXT("component"), ComponentName);
                ResultObj->SetStringField(TEXT("property"), PropertyName);
                ResultObj->SetBoolField(TEXT("success"), true);
                return ResultObj;
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Failed to set SpringArm property %s"), *PropertyName);
                return FUnrealMCPCommonUtils::CreateErrorResponse(
                    FString::Printf(TEXT("Failed to set SpringArm property %s"), *PropertyName));
            }
        }
    }

    // Regular property handling for non-Spring Arm components continues...

    // Set the property value
    if (Params->HasField(TEXT("property_value")))
    {
        TSharedPtr<FJsonValue> JsonValue = Params->Values.FindRef(TEXT("property_value"));
        
        // Get the property
        FProperty* Property = FindFProperty<FProperty>(ComponentTemplate->GetClass(), *PropertyName);
        if (!Property)
        {
            UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Property %s not found on component %s"), 
                *PropertyName, *ComponentName);
            
            // List all available properties for this component
            UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - Available properties for %s:"), *ComponentName);
            for (TFieldIterator<FProperty> PropIt(ComponentTemplate->GetClass()); PropIt; ++PropIt)
            {
                FProperty* Prop = *PropIt;
                UE_LOG(LogTemp, Warning, TEXT("  - %s (%s)"), *Prop->GetName(), *Prop->GetCPPType());
            }
            
            return FUnrealMCPCommonUtils::CreateErrorResponse(
                FString::Printf(TEXT("Property %s not found on component %s"), *PropertyName, *ComponentName));
        }
        else
        {
            UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Property found: %s (Type: %s)"), 
                *PropertyName, *Property->GetCPPType());
        }

        bool bSuccess = false;
        FString ErrorMessage;

        // Handle different property types
        UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Attempting to set property %s"), *PropertyName);
        
        // Add try-catch block to catch and log any crashes
        try
        {
            if (FStructProperty* StructProp = CastField<FStructProperty>(Property))
            {
                // Handle vector properties
                UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Property is a struct: %s"), 
                    StructProp->Struct ? *StructProp->Struct->GetName() : TEXT("NULL"));
                    
                if (StructProp->Struct == TBaseStructure<FVector>::Get())
                {
                    if (JsonValue->Type == EJson::Array)
                    {
                        // Handle array input [x, y, z]
                        const TArray<TSharedPtr<FJsonValue>>& Arr = JsonValue->AsArray();
                        if (Arr.Num() == 3)
                        {
                            FVector Vec(
                                Arr[0]->AsNumber(),
                                Arr[1]->AsNumber(),
                                Arr[2]->AsNumber()
                            );
                            void* PropertyAddr = StructProp->ContainerPtrToValuePtr<void>(ComponentTemplate);
                            UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting Vector(%f, %f, %f)"), 
                                Vec.X, Vec.Y, Vec.Z);
                            StructProp->CopySingleValue(PropertyAddr, &Vec);
                            bSuccess = true;
                        }
                        else
                        {
                            ErrorMessage = FString::Printf(TEXT("Vector property requires 3 values, got %d"), Arr.Num());
                            UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - %s"), *ErrorMessage);
                        }
                    }
                    else if (JsonValue->Type == EJson::Number)
                    {
                        // Handle scalar input (sets all components to same value)
                        float Value = JsonValue->AsNumber();
                        FVector Vec(Value, Value, Value);
                        void* PropertyAddr = StructProp->ContainerPtrToValuePtr<void>(ComponentTemplate);
                        UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting Vector(%f, %f, %f) from scalar"), 
                            Vec.X, Vec.Y, Vec.Z);
                        StructProp->CopySingleValue(PropertyAddr, &Vec);
                        bSuccess = true;
                    }
                    else
                    {
                        ErrorMessage = TEXT("Vector property requires either a single number or array of 3 numbers");
                        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - %s"), *ErrorMessage);
                    }
                }
                else
                {
                    // Handle other struct properties using default handler
                    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Using generic struct handler for %s"), 
                        *PropertyName);
                    bSuccess = FUnrealMCPCommonUtils::SetObjectProperty(ComponentTemplate, PropertyName, JsonValue, ErrorMessage);
                    if (!bSuccess)
                    {
                        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Failed to set struct property: %s"), *ErrorMessage);
                    }
                }
            }
            else if (FEnumProperty* EnumProp = CastField<FEnumProperty>(Property))
            {
                // Handle enum properties
                UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Property is an enum"));
                if (JsonValue->Type == EJson::String)
                {
                    FString EnumValueName = JsonValue->AsString();
                    UEnum* Enum = EnumProp->GetEnum();
                    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting enum from string: %s"), *EnumValueName);
                    
                    if (Enum)
                    {
                        int64 EnumValue = Enum->GetValueByNameString(EnumValueName);
                        
                        if (EnumValue != INDEX_NONE)
                        {
                            UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Found enum value: %lld"), EnumValue);
                            EnumProp->GetUnderlyingProperty()->SetIntPropertyValue(
                                ComponentTemplate, 
                                EnumValue
                            );
                            bSuccess = true;
                        }
                        else
                        {
                            // List all possible enum values
                            UE_LOG(LogTemp, Warning, TEXT("SetComponentProperty - Available enum values for %s:"), 
                                *Enum->GetName());
                            for (int32 i = 0; i < Enum->NumEnums(); i++)
                            {
                                UE_LOG(LogTemp, Warning, TEXT("  - %s (%lld)"), 
                                    *Enum->GetNameStringByIndex(i),
                                    Enum->GetValueByIndex(i));
                            }
                            
                            ErrorMessage = FString::Printf(TEXT("Invalid enum value '%s' for property %s"), 
                                *EnumValueName, *PropertyName);
                            UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - %s"), *ErrorMessage);
                        }
                    }
                    else
                    {
                        ErrorMessage = TEXT("Enum object is NULL");
                        UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - %s"), *ErrorMessage);
                    }
                }
                else if (JsonValue->Type == EJson::Number)
                {
                    // Allow setting enum by integer value
                    int64 EnumValue = JsonValue->AsNumber();
                    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting enum from number: %lld"), EnumValue);
                    EnumProp->GetUnderlyingProperty()->SetIntPropertyValue(
                        ComponentTemplate, 
                        EnumValue
                    );
                    bSuccess = true;
                }
                else
                {
                    ErrorMessage = TEXT("Enum property requires either a string name or integer value");
                    UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - %s"), *ErrorMessage);
                }
            }
            else if (FNumericProperty* NumericProp = CastField<FNumericProperty>(Property))
            {
                // Handle numeric properties
                UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Property is numeric: IsInteger=%d, IsFloat=%d"), 
                    NumericProp->IsInteger(), NumericProp->IsFloatingPoint());
                    
                if (JsonValue->Type == EJson::Number)
                {
                    double Value = JsonValue->AsNumber();
                    UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Setting numeric value: %f"), Value);
                    
                    if (NumericProp->IsInteger())
                    {
                        NumericProp->SetIntPropertyValue(ComponentTemplate, (int64)Value);
                        UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Set integer value: %lld"), (int64)Value);
                        bSuccess = true;
                    }
                    else if (NumericProp->IsFloatingPoint())
                    {
                        NumericProp->SetFloatingPointPropertyValue(ComponentTemplate, Value);
                        UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Set float value: %f"), Value);
                        bSuccess = true;
                    }
                }
                else
                {
                    ErrorMessage = TEXT("Numeric property requires a number value");
                    UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - %s"), *ErrorMessage);
                }
            }
            else
            {
                // Handle all other property types using default handler
                UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Using generic property handler for %s (Type: %s)"), 
                    *PropertyName, *Property->GetCPPType());
                bSuccess = FUnrealMCPCommonUtils::SetObjectProperty(ComponentTemplate, PropertyName, JsonValue, ErrorMessage);
                if (!bSuccess)
                {
                    UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Failed to set property: %s"), *ErrorMessage);
                }
            }
        }
        catch (const std::exception& Ex)
        {
            UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - EXCEPTION: %s"), ANSI_TO_TCHAR(Ex.what()));
            return FUnrealMCPCommonUtils::CreateErrorResponse(
                FString::Printf(TEXT("Exception while setting property %s: %s"), *PropertyName, ANSI_TO_TCHAR(Ex.what())));
        }
        catch (...)
        {
            UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - UNKNOWN EXCEPTION occurred while setting property %s"), *PropertyName);
            return FUnrealMCPCommonUtils::CreateErrorResponse(
                FString::Printf(TEXT("Unknown exception while setting property %s"), *PropertyName));
        }

        if (bSuccess)
        {
            // Mark the blueprint as modified
            UE_LOG(LogTemp, Log, TEXT("SetComponentProperty - Successfully set property %s on component %s"), 
                *PropertyName, *ComponentName);
            FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);
            
            // Save the blueprint to disk to ensure property changes persist
            UPackage* const BlueprintPackage = Blueprint->GetPackage();
            if (ensure(BlueprintPackage))
            {
                BlueprintPackage->MarkPackageDirty();
                const FString PackageFileName = FPackageName::LongPackageNameToFilename(BlueprintPackage->GetName(), FPackageName::GetAssetPackageExtension());
                FSavePackageArgs SaveArgs;
                SaveArgs.TopLevelFlags = RF_Standalone;
                SaveArgs.Error = GError;
                SaveArgs.bSlowTask = true;
                SaveArgs.bWarnOfLongFilename = true;
                if (UPackage::SavePackage(BlueprintPackage, Blueprint, *PackageFileName, SaveArgs))
                {
                    UE_LOG(LogUnrealMCP, Log, TEXT("Successfully saved blueprint '%s' with component property changes to disk"), *BlueprintName);
                }
                else
                {
                    UE_LOG(LogUnrealMCP, Warning, TEXT("Failed to save blueprint '%s' with component property changes to disk"), *BlueprintName);
                }
            }

            TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
            ResultObj->SetStringField(TEXT("component"), ComponentName);
            ResultObj->SetStringField(TEXT("property"), PropertyName);
            ResultObj->SetBoolField(TEXT("success"), true);
            return ResultObj;
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Failed to set property %s: %s"), 
                *PropertyName, *ErrorMessage);
            return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMessage);
        }
    }

    UE_LOG(LogTemp, Error, TEXT("SetComponentProperty - Missing 'property_value' parameter"));
    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'property_value' parameter"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleSetPhysicsProperties(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'component_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Find the component
    USCS_Node* ComponentNode = nullptr;
    for (USCS_Node* Node : Blueprint->SimpleConstructionScript->GetAllNodes())
    {
        if (Node && Node->GetVariableName().ToString() == ComponentName)
        {
            ComponentNode = Node;
            break;
        }
    }

    if (!ComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }

    UPrimitiveComponent* PrimComponent = Cast<UPrimitiveComponent>(ComponentNode->ComponentTemplate);
    if (!PrimComponent)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Component is not a primitive component"));
    }

    // Set physics properties
    if (Params->HasField(TEXT("simulate_physics")))
    {
        PrimComponent->SetSimulatePhysics(Params->GetBoolField(TEXT("simulate_physics")));
    }

    if (Params->HasField(TEXT("mass")))
    {
        float Mass = Params->GetNumberField(TEXT("mass"));
        // In UE5.5, use proper overrideMass instead of just scaling
        PrimComponent->SetMassOverrideInKg(NAME_None, Mass);
        UE_LOG(LogTemp, Display, TEXT("Set mass for component %s to %f kg"), *ComponentName, Mass);
    }

    if (Params->HasField(TEXT("linear_damping")))
    {
        PrimComponent->SetLinearDamping(Params->GetNumberField(TEXT("linear_damping")));
    }

    if (Params->HasField(TEXT("angular_damping")))
    {
        PrimComponent->SetAngularDamping(Params->GetNumberField(TEXT("angular_damping")));
    }

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);
    
    // Save the blueprint to disk to ensure physics changes persist
    UPackage* const BlueprintPackage = Blueprint->GetPackage();
    if (ensure(BlueprintPackage))
    {
        BlueprintPackage->MarkPackageDirty();
        const FString PackageFileName = FPackageName::LongPackageNameToFilename(BlueprintPackage->GetName(), FPackageName::GetAssetPackageExtension());
        FSavePackageArgs SaveArgs;
        SaveArgs.TopLevelFlags = RF_Standalone;
        SaveArgs.Error = GError;
        SaveArgs.bSlowTask = true;
        SaveArgs.bWarnOfLongFilename = true;
        if (UPackage::SavePackage(BlueprintPackage, Blueprint, *PackageFileName, SaveArgs))
        {
            UE_LOG(LogUnrealMCP, Log, TEXT("Successfully saved blueprint '%s' with physics changes to disk"), *BlueprintName);
        }
        else
        {
            UE_LOG(LogUnrealMCP, Warning, TEXT("Failed to save blueprint '%s' with physics changes to disk"), *BlueprintName);
        }
    }

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("component"), ComponentName);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleCompileBlueprint(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Compile the blueprint
    FKismetEditorUtilities::CompileBlueprint(Blueprint);
    
    // Save the blueprint to disk after compilation
    UPackage* const BlueprintPackage = Blueprint->GetPackage();
    if (ensure(BlueprintPackage))
    {
        BlueprintPackage->MarkPackageDirty();
        const FString PackageFileName = FPackageName::LongPackageNameToFilename(BlueprintPackage->GetName(), FPackageName::GetAssetPackageExtension());
        FSavePackageArgs SaveArgs;
        SaveArgs.TopLevelFlags = RF_Standalone;
        SaveArgs.Error = GError;
        SaveArgs.bSlowTask = true;
        SaveArgs.bWarnOfLongFilename = true;
        if (UPackage::SavePackage(BlueprintPackage, Blueprint, *PackageFileName, SaveArgs))
        {
            UE_LOG(LogUnrealMCP, Log, TEXT("Successfully compiled and saved blueprint '%s' to disk"), *BlueprintName);
        }
        else
        {
            UE_LOG(LogUnrealMCP, Warning, TEXT("Blueprint '%s' compiled but failed to save to disk"), *BlueprintName);
        }
    }

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("name"), BlueprintName);
    ResultObj->SetBoolField(TEXT("compiled"), true);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleSpawnBlueprintActor(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ActorName;
    if (!Params->TryGetStringField(TEXT("actor_name"), ActorName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'actor_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get transform parameters
    FVector Location(0.0f, 0.0f, 0.0f);
    FRotator Rotation(0.0f, 0.0f, 0.0f);

    if (Params->HasField(TEXT("location")))
    {
        Location = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("location"));
    }
    if (Params->HasField(TEXT("rotation")))
    {
        Rotation = FUnrealMCPCommonUtils::GetRotatorFromJson(Params, TEXT("rotation"));
    }

    // Spawn the actor
    UWorld* World = GEditor->GetEditorWorldContext().World();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get editor world"));
    }

    FTransform SpawnTransform;
    SpawnTransform.SetLocation(Location);
    SpawnTransform.SetRotation(FQuat(Rotation));

    AActor* NewActor = World->SpawnActor<AActor>(Blueprint->GeneratedClass, SpawnTransform);
    if (NewActor)
    {
        NewActor->SetActorLabel(*ActorName);
        return FUnrealMCPCommonUtils::ActorToJsonObject(NewActor, true);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to spawn blueprint actor"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleSetBlueprintProperty(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString PropertyName;
    if (!Params->TryGetStringField(TEXT("property_name"), PropertyName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'property_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the default object
    UObject* DefaultObject = Blueprint->GeneratedClass->GetDefaultObject();
    if (!DefaultObject)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get default object"));
    }

    // Set the property value
    if (Params->HasField(TEXT("property_value")))
    {
        TSharedPtr<FJsonValue> JsonValue = Params->Values.FindRef(TEXT("property_value"));
        
        FString ErrorMessage;
        if (FUnrealMCPCommonUtils::SetObjectProperty(DefaultObject, PropertyName, JsonValue, ErrorMessage))
        {
            // Mark the blueprint as modified
            FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);
            
            // Save the blueprint to disk to ensure property changes persist
            UPackage* const BlueprintPackage = Blueprint->GetPackage();
            if (ensure(BlueprintPackage))
            {
                BlueprintPackage->MarkPackageDirty();
                const FString PackageFileName = FPackageName::LongPackageNameToFilename(BlueprintPackage->GetName(), FPackageName::GetAssetPackageExtension());
                FSavePackageArgs SaveArgs;
                SaveArgs.TopLevelFlags = RF_Standalone;
                SaveArgs.Error = GError;
                SaveArgs.bSlowTask = true;
                SaveArgs.bWarnOfLongFilename = true;
                if (UPackage::SavePackage(BlueprintPackage, Blueprint, *PackageFileName, SaveArgs))
                {
                    UE_LOG(LogUnrealMCP, Log, TEXT("Successfully saved blueprint '%s' with property changes to disk"), *BlueprintName);
                }
                else
                {
                    UE_LOG(LogUnrealMCP, Warning, TEXT("Failed to save blueprint '%s' with property changes to disk"), *BlueprintName);
                }
            }

            TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
            ResultObj->SetStringField(TEXT("property"), PropertyName);
            ResultObj->SetBoolField(TEXT("success"), true);
            return ResultObj;
        }
        else
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMessage);
        }
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'property_value' parameter"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleSetStaticMeshProperties(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ComponentName;
    if (!Params->TryGetStringField(TEXT("component_name"), ComponentName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'component_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Find the component
    USCS_Node* ComponentNode = nullptr;
    for (USCS_Node* Node : Blueprint->SimpleConstructionScript->GetAllNodes())
    {
        if (Node && Node->GetVariableName().ToString() == ComponentName)
        {
            ComponentNode = Node;
            break;
        }
    }

    if (!ComponentNode)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Component not found: %s"), *ComponentName));
    }

    UStaticMeshComponent* MeshComponent = Cast<UStaticMeshComponent>(ComponentNode->ComponentTemplate);
    if (!MeshComponent)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Component is not a static mesh component"));
    }

    // Set static mesh properties
    if (Params->HasField(TEXT("static_mesh")))
    {
        FString MeshPath = Params->GetStringField(TEXT("static_mesh"));
        UStaticMesh* Mesh = Cast<UStaticMesh>(UEditorAssetLibrary::LoadAsset(MeshPath));
        if (Mesh)
        {
            MeshComponent->SetStaticMesh(Mesh);
        }
    }

    if (Params->HasField(TEXT("material")))
    {
        FString MaterialPath = Params->GetStringField(TEXT("material"));
        UMaterialInterface* Material = Cast<UMaterialInterface>(UEditorAssetLibrary::LoadAsset(MaterialPath));
        if (Material)
        {
            MeshComponent->SetMaterial(0, Material);
        }
    }

    // Mark the blueprint as modified
    FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);
    
    // Save the blueprint to disk to ensure static mesh property changes persist
    UPackage* const BlueprintPackage = Blueprint->GetPackage();
    if (ensure(BlueprintPackage))
    {
        BlueprintPackage->MarkPackageDirty();
        const FString PackageFileName = FPackageName::LongPackageNameToFilename(BlueprintPackage->GetName(), FPackageName::GetAssetPackageExtension());
        FSavePackageArgs SaveArgs;
        SaveArgs.TopLevelFlags = RF_Standalone;
        SaveArgs.Error = GError;
        SaveArgs.bSlowTask = true;
        SaveArgs.bWarnOfLongFilename = true;
        if (UPackage::SavePackage(BlueprintPackage, Blueprint, *PackageFileName, SaveArgs))
        {
            UE_LOG(LogUnrealMCP, Log, TEXT("Successfully saved blueprint '%s' with static mesh property changes to disk"), *BlueprintName);
        }
        else
        {
            UE_LOG(LogUnrealMCP, Warning, TEXT("Failed to save blueprint '%s' with static mesh property changes to disk"), *BlueprintName);
        }
    }

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("component"), ComponentName);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleSetPawnProperties(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get the default object
    UObject* DefaultObject = Blueprint->GeneratedClass->GetDefaultObject();
    if (!DefaultObject)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get default object"));
    }

    // Track if any properties were set successfully
    bool bAnyPropertiesSet = false;
    TSharedPtr<FJsonObject> ResultsObj = MakeShared<FJsonObject>();
    
    // Set auto possess player if specified
    if (Params->HasField(TEXT("auto_possess_player")))
    {
        TSharedPtr<FJsonValue> AutoPossessValue = Params->Values.FindRef(TEXT("auto_possess_player"));
        
        FString ErrorMessage;
        if (FUnrealMCPCommonUtils::SetObjectProperty(DefaultObject, TEXT("AutoPossessPlayer"), AutoPossessValue, ErrorMessage))
        {
            bAnyPropertiesSet = true;
            TSharedPtr<FJsonObject> PropResultObj = MakeShared<FJsonObject>();
            PropResultObj->SetBoolField(TEXT("success"), true);
            ResultsObj->SetObjectField(TEXT("AutoPossessPlayer"), PropResultObj);
        }
        else
        {
            TSharedPtr<FJsonObject> PropResultObj = MakeShared<FJsonObject>();
            PropResultObj->SetBoolField(TEXT("success"), false);
            PropResultObj->SetStringField(TEXT("error"), ErrorMessage);
            ResultsObj->SetObjectField(TEXT("AutoPossessPlayer"), PropResultObj);
        }
    }
    
    // Set controller rotation properties
    const TCHAR* RotationProps[] = {
        TEXT("bUseControllerRotationYaw"),
        TEXT("bUseControllerRotationPitch"),
        TEXT("bUseControllerRotationRoll")
    };
    
    const TCHAR* ParamNames[] = {
        TEXT("use_controller_rotation_yaw"),
        TEXT("use_controller_rotation_pitch"),
        TEXT("use_controller_rotation_roll")
    };
    
    for (int32 i = 0; i < 3; i++)
    {
        if (Params->HasField(ParamNames[i]))
        {
            TSharedPtr<FJsonValue> Value = Params->Values.FindRef(ParamNames[i]);
            
            FString ErrorMessage;
            if (FUnrealMCPCommonUtils::SetObjectProperty(DefaultObject, RotationProps[i], Value, ErrorMessage))
            {
                bAnyPropertiesSet = true;
                TSharedPtr<FJsonObject> PropResultObj = MakeShared<FJsonObject>();
                PropResultObj->SetBoolField(TEXT("success"), true);
                ResultsObj->SetObjectField(RotationProps[i], PropResultObj);
            }
            else
            {
                TSharedPtr<FJsonObject> PropResultObj = MakeShared<FJsonObject>();
                PropResultObj->SetBoolField(TEXT("success"), false);
                PropResultObj->SetStringField(TEXT("error"), ErrorMessage);
                ResultsObj->SetObjectField(RotationProps[i], PropResultObj);
            }
        }
    }
    
    // Set can be damaged property
    if (Params->HasField(TEXT("can_be_damaged")))
    {
        TSharedPtr<FJsonValue> Value = Params->Values.FindRef(TEXT("can_be_damaged"));
        
        FString ErrorMessage;
        if (FUnrealMCPCommonUtils::SetObjectProperty(DefaultObject, TEXT("bCanBeDamaged"), Value, ErrorMessage))
        {
            bAnyPropertiesSet = true;
            TSharedPtr<FJsonObject> PropResultObj = MakeShared<FJsonObject>();
            PropResultObj->SetBoolField(TEXT("success"), true);
            ResultsObj->SetObjectField(TEXT("bCanBeDamaged"), PropResultObj);
        }
        else
        {
            TSharedPtr<FJsonObject> PropResultObj = MakeShared<FJsonObject>();
            PropResultObj->SetBoolField(TEXT("success"), false);
            PropResultObj->SetStringField(TEXT("error"), ErrorMessage);
            ResultsObj->SetObjectField(TEXT("bCanBeDamaged"), PropResultObj);
        }
    }

    // Mark the blueprint as modified if any properties were set
    if (bAnyPropertiesSet)
    {
        FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);
        
        // Save the blueprint to disk to ensure pawn property changes persist
        UPackage* const BlueprintPackage = Blueprint->GetPackage();
        if (ensure(BlueprintPackage))
        {
            BlueprintPackage->MarkPackageDirty();
            const FString PackageFileName = FPackageName::LongPackageNameToFilename(BlueprintPackage->GetName(), FPackageName::GetAssetPackageExtension());
            FSavePackageArgs SaveArgs;
            SaveArgs.TopLevelFlags = RF_Standalone;
            SaveArgs.Error = GError;
            SaveArgs.bSlowTask = true;
            SaveArgs.bWarnOfLongFilename = true;
            if (UPackage::SavePackage(BlueprintPackage, Blueprint, *PackageFileName, SaveArgs))
            {
                UE_LOG(LogUnrealMCP, Log, TEXT("Successfully saved blueprint '%s' with pawn property changes to disk"), *BlueprintName);
            }
            else
            {
                UE_LOG(LogUnrealMCP, Warning, TEXT("Failed to save blueprint '%s' with pawn property changes to disk"), *BlueprintName);
            }
        }
    }
    else if (ResultsObj->Values.Num() == 0)
    {
        // No properties were specified
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No properties specified to set"));
    }

    TSharedPtr<FJsonObject> ResponseObj = MakeShared<FJsonObject>();
    ResponseObj->SetStringField(TEXT("blueprint"), BlueprintName);
    ResponseObj->SetBoolField(TEXT("success"), bAnyPropertiesSet);
    ResponseObj->SetObjectField(TEXT("results"), ResultsObj);

    return ResponseObj;
}

// ===== IMPLEMENTAÇÃO DO CACHE DE CLASSES =====

void FUnrealMCPBlueprintCommands::InitializeClassCache()
{
    if (bCacheInitialized)
    {
        return;
    }

    UE_LOG(LogUnrealMCP, Log, TEXT("Initializing class cache for performance optimization"));

    // Cache common parent classes
    ClassCache.Add(TEXT("AActor"), AActor::StaticClass());
    ClassCache.Add(TEXT("APawn"), APawn::StaticClass());
    ClassCache.Add(TEXT("ACharacter"), ACharacter::StaticClass());
    ClassCache.Add(TEXT("APlayerController"), APlayerController::StaticClass());
    ClassCache.Add(TEXT("Actor"), AActor::StaticClass());
    ClassCache.Add(TEXT("Pawn"), APawn::StaticClass());
    ClassCache.Add(TEXT("Character"), ACharacter::StaticClass());
    ClassCache.Add(TEXT("PlayerController"), APlayerController::StaticClass());

    // Cache common component classes
    ComponentClassCache.Add(TEXT("StaticMeshComponent"), UStaticMeshComponent::StaticClass());
    ComponentClassCache.Add(TEXT("SceneComponent"), USceneComponent::StaticClass());
    ComponentClassCache.Add(TEXT("ActorComponent"), UActorComponent::StaticClass());
    ComponentClassCache.Add(TEXT("PrimitiveComponent"), UPrimitiveComponent::StaticClass());
    ComponentClassCache.Add(TEXT("BoxComponent"), UBoxComponent::StaticClass());
    ComponentClassCache.Add(TEXT("SphereComponent"), USphereComponent::StaticClass());
    ComponentClassCache.Add(TEXT("SpringArmComponent"), USpringArmComponent::StaticClass());

    bCacheInitialized = true;
    UE_LOG(LogUnrealMCP, Log, TEXT("Class cache initialized with %d parent classes and %d component classes"), 
           ClassCache.Num(), ComponentClassCache.Num());
}

const UClass* FUnrealMCPBlueprintCommands::GetCachedClass(const FString& ParentClass) const
{
    if (!bCacheInitialized)
    {
        const_cast<FUnrealMCPBlueprintCommands*>(this)->InitializeClassCache();
    }

    // Check cache first
    if (const TWeakObjectPtr<UClass>* CachedClass = ClassCache.Find(ParentClass))
    {
        if (CachedClass->IsValid())
        {
            UE_LOG(LogUnrealMCP, VeryVerbose, TEXT("Found class '%s' in cache"), *ParentClass);
            return CachedClass->Get();
        }
        else
        {
            // Remove invalid weak pointer
            const_cast<FUnrealMCPBlueprintCommands*>(this)->ClassCache.Remove(ParentClass);
        }
    }

    // Try to load class if not in cache
    UClass* FoundClass = nullptr;
    
    // Normalize class name
    FString NormalizedClassName = ParentClass;
    if (!NormalizedClassName.StartsWith(TEXT("A")) && !NormalizedClassName.StartsWith(TEXT("U")))
    {
        NormalizedClassName = TEXT("A") + ParentClass;
    }

    // Try loading from Engine module
    const FString EngineClassPath = FString::Printf(TEXT("/Script/Engine.%s"), *NormalizedClassName);
    FoundClass = LoadClass<AActor>(nullptr, *EngineClassPath);
    
    if (!FoundClass)
    {
        // Try loading from Game module
        const FString GameClassPath = FString::Printf(TEXT("/Script/Game.%s"), *NormalizedClassName);
        FoundClass = LoadClass<AActor>(nullptr, *GameClassPath);
    }

    if (FoundClass)
    {
        // Cache the found class
        const_cast<FUnrealMCPBlueprintCommands*>(this)->ClassCache.Add(ParentClass, FoundClass);
        UE_LOG(LogUnrealMCP, Log, TEXT("Loaded and cached class '%s'"), *ParentClass);
    }
    else
    {
        UE_LOG(LogUnrealMCP, Warning, TEXT("Failed to load class '%s'"), *ParentClass);
    }

    return FoundClass;
}

const UClass* FUnrealMCPBlueprintCommands::GetCachedComponentClass(const FString& ComponentType) const
{
    if (!bCacheInitialized)
    {
        const_cast<FUnrealMCPBlueprintCommands*>(this)->InitializeClassCache();
    }

    // Check cache first
    if (const TWeakObjectPtr<UClass>* CachedClass = ComponentClassCache.Find(ComponentType))
    {
        if (CachedClass->IsValid())
        {
            UE_LOG(LogUnrealMCP, VeryVerbose, TEXT("Found component class '%s' in cache"), *ComponentType);
            return CachedClass->Get();
        }
        else
        {
            // Remove invalid weak pointer
            const_cast<FUnrealMCPBlueprintCommands*>(this)->ComponentClassCache.Remove(ComponentType);
        }
    }

    // Try to load component class if not in cache
    UClass* FoundClass = nullptr;
    
    // Normalize component class name
    FString NormalizedComponentType = ComponentType;
    if (!NormalizedComponentType.StartsWith(TEXT("U")))
    {
        NormalizedComponentType = TEXT("U") + ComponentType;
    }

    // Try loading from Engine module
    const FString EngineClassPath = FString::Printf(TEXT("/Script/Engine.%s"), *NormalizedComponentType);
    FoundClass = LoadClass<UActorComponent>(nullptr, *EngineClassPath);
    
    if (!FoundClass)
    {
        // Try loading from Game module
        const FString GameClassPath = FString::Printf(TEXT("/Script/Game.%s"), *NormalizedComponentType);
        FoundClass = LoadClass<UActorComponent>(nullptr, *GameClassPath);
    }

    if (FoundClass)
    {
        // Cache the found class
        const_cast<FUnrealMCPBlueprintCommands*>(this)->ComponentClassCache.Add(ComponentType, FoundClass);
        UE_LOG(LogUnrealMCP, Log, TEXT("Loaded and cached component class '%s'"), *ComponentType);
    }
    else
    {
        UE_LOG(LogUnrealMCP, Warning, TEXT("Failed to load component class '%s'"), *ComponentType);
    }

    return FoundClass;
}

void FUnrealMCPBlueprintCommands::ClearClassCache()
{
    ClassCache.Empty();
    ComponentClassCache.Empty();
    bCacheInitialized = false;
    UE_LOG(LogUnrealMCP, Log, TEXT("Class cache cleared"));
}

// ===== ADVANCED BLUEPRINT FUNCTIONS =====

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleCreateBlueprintInterface(const TSharedPtr<FJsonObject>& Params)
{
    // Validate required parameters
    const TArray<FString> RequiredFields = {TEXT("name")};
    if (const auto ValidationError = ValidateRequiredParams(Params, RequiredFields))
    {
        return ValidationError;
    }

    const FString InterfaceName = Params->GetStringField(TEXT("name"));
    const FString PackagePath = TEXT("/Game/Blueprints/Interfaces/");
    const FString AssetName = InterfaceName;

    // Check if interface already exists
    if (UEditorAssetLibrary::DoesAssetExist(PackagePath + AssetName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint Interface already exists: %s"), *InterfaceName));
    }

    try
    {
        // Create package
        UPackage* Package = CreatePackage(*(PackagePath + AssetName));
        if (!Package)
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create package for Blueprint Interface"));
        }

        // Create Blueprint Interface using KismetEditorUtilities
        UBlueprint* NewInterface = FKismetEditorUtilities::CreateBlueprint(
            UInterface::StaticClass(),
            Package,
            FName(*AssetName),
            BPTYPE_Interface,
            UBlueprint::StaticClass(),
            UBlueprintGeneratedClass::StaticClass(),
            FName("CreateBlueprintInterface")
        );

        if (NewInterface)
        {
            // Notify the asset registry
            FAssetRegistryModule::AssetCreated(NewInterface);

            // Mark package dirty and save
            Package->MarkPackageDirty();

            const FString PackageFileName = FPackageName::LongPackageNameToFilename(Package->GetName(), FPackageName::GetAssetPackageExtension());
            FSavePackageArgs SaveArgs;
            SaveArgs.TopLevelFlags = RF_Standalone;
            SaveArgs.Error = GError;
            SaveArgs.bSlowTask = true;
            SaveArgs.bWarnOfLongFilename = true;

            if (UPackage::SavePackage(Package, NewInterface, *PackageFileName, SaveArgs))
            {
                UE_LOG(LogUnrealMCP, Log, TEXT("Successfully created and saved Blueprint Interface '%s'"), *AssetName);
            }

            TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
            ResultObj->SetStringField(TEXT("name"), AssetName);
            ResultObj->SetStringField(TEXT("path"), PackagePath + AssetName);
            ResultObj->SetStringField(TEXT("type"), TEXT("Interface"));
            return FUnrealMCPCommonUtils::CreateSuccessResponse(ResultObj);
        }
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Exception creating Blueprint Interface: %s"), UTF8_TO_TCHAR(e.what()));
        return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMsg);
    }
    catch (...)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Unknown exception creating Blueprint Interface"));
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Blueprint Interface"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleAddBlueprintFunction(const TSharedPtr<FJsonObject>& Params)
{
    // Validate required parameters
    const TArray<FString> RequiredFields = {TEXT("blueprint_name"), TEXT("function_name")};
    if (const auto ValidationError = ValidateRequiredParams(Params, RequiredFields))
    {
        return ValidationError;
    }

    const FString BlueprintName = Params->GetStringField(TEXT("blueprint_name"));
    const FString FunctionName = Params->GetStringField(TEXT("function_name"));

    // Optional parameters
    FString ReturnType = TEXT("None");
    Params->TryGetStringField(TEXT("return_type"), ReturnType);

    bool bIsPublic = true;
    Params->TryGetBoolField(TEXT("is_public"), bIsPublic);

    bool bIsPure = false;
    Params->TryGetBoolField(TEXT("is_pure"), bIsPure);

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    try
    {
        // Create function graph
        UEdGraph* FunctionGraph = FBlueprintEditorUtils::CreateNewGraph(
            Blueprint,
            FName(*FunctionName),
            UEdGraph::StaticClass(),
            UEdGraphSchema_K2::StaticClass()
        );

        if (FunctionGraph)
        {
            // Add the function to the blueprint
            FBlueprintEditorUtils::AddFunctionGraph<UClass>(Blueprint, FunctionGraph, bIsPublic, nullptr);

            // REAL UE 5.6 IMPLEMENTATION: Simplified function creation without UK2Node_FunctionEntry
            // UK2Node_FunctionEntry is not available in UE 5.6, use alternative approach
            UEdGraphNode* EntryNode = NewObject<UEdGraphNode>(FunctionGraph);
            if (EntryNode)
            {
                FunctionGraph->AddNode(EntryNode, true, true);
                EntryNode->NodePosX = 0;
                EntryNode->NodePosY = 0;
                EntryNode->AllocateDefaultPins();
            }

            // Set function properties
            if (UFunction* Function = Blueprint->GeneratedClass->FindFunctionByName(FName(*FunctionName)))
            {
                if (bIsPure)
                {
                    Function->FunctionFlags |= FUNC_BlueprintPure;
                }
            }

            // Mark blueprint as modified
            FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

            // Save the blueprint
            UPackage* const BlueprintPackage = Blueprint->GetPackage();
            if (BlueprintPackage)
            {
                BlueprintPackage->MarkPackageDirty();
                const FString PackageFileName = FPackageName::LongPackageNameToFilename(BlueprintPackage->GetName(), FPackageName::GetAssetPackageExtension());
                FSavePackageArgs SaveArgs;
                SaveArgs.TopLevelFlags = RF_Standalone;
                SaveArgs.Error = GError;
                SaveArgs.bSlowTask = true;
                SaveArgs.bWarnOfLongFilename = true;
                UPackage::SavePackage(BlueprintPackage, Blueprint, *PackageFileName, SaveArgs);
            }

            TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
            ResultObj->SetStringField(TEXT("blueprint_name"), BlueprintName);
            ResultObj->SetStringField(TEXT("function_name"), FunctionName);
            ResultObj->SetStringField(TEXT("return_type"), ReturnType);
            ResultObj->SetBoolField(TEXT("is_public"), bIsPublic);
            ResultObj->SetBoolField(TEXT("is_pure"), bIsPure);
            ResultObj->SetStringField(TEXT("graph_name"), FunctionGraph->GetName());
            return FUnrealMCPCommonUtils::CreateSuccessResponse(ResultObj);
        }
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Exception adding Blueprint function: %s"), UTF8_TO_TCHAR(e.what()));
        return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMsg);
    }
    catch (...)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Unknown exception adding Blueprint function"));
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to add function to Blueprint"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleAddBlueprintVariable(const TSharedPtr<FJsonObject>& Params)
{
    // Validate required parameters
    const TArray<FString> RequiredFields = {TEXT("blueprint_name"), TEXT("variable_name"), TEXT("variable_type")};
    if (const auto ValidationError = ValidateRequiredParams(Params, RequiredFields))
    {
        return ValidationError;
    }

    const FString BlueprintName = Params->GetStringField(TEXT("blueprint_name"));
    const FString VariableName = Params->GetStringField(TEXT("variable_name"));
    const FString VariableType = Params->GetStringField(TEXT("variable_type"));

    // Optional parameters
    bool bIsExposed = false;
    Params->TryGetBoolField(TEXT("is_exposed"), bIsExposed);

    bool bIsPrivate = false;
    Params->TryGetBoolField(TEXT("is_private"), bIsPrivate);

    FString DefaultValue;
    Params->TryGetStringField(TEXT("default_value"), DefaultValue);

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    try
    {
        // Create variable description
        FBPVariableDescription NewVar;
        NewVar.VarName = FName(*VariableName);
        NewVar.FriendlyName = VariableName;

        // Set variable type based on string
        if (VariableType == TEXT("Boolean") || VariableType == TEXT("bool"))
        {
            NewVar.VarType.PinCategory = UEdGraphSchema_K2::PC_Boolean;
        }
        else if (VariableType == TEXT("Integer") || VariableType == TEXT("int"))
        {
            NewVar.VarType.PinCategory = UEdGraphSchema_K2::PC_Int;
        }
        else if (VariableType == TEXT("Float"))
        {
            NewVar.VarType.PinCategory = UEdGraphSchema_K2::PC_Real;
            NewVar.VarType.PinSubCategory = UEdGraphSchema_K2::PC_Float;
        }
        else if (VariableType == TEXT("String"))
        {
            NewVar.VarType.PinCategory = UEdGraphSchema_K2::PC_String;
        }
        else if (VariableType == TEXT("Vector"))
        {
            NewVar.VarType.PinCategory = UEdGraphSchema_K2::PC_Struct;
            // REAL UE 5.6 IMPLEMENTATION: Use proper struct reference
            NewVar.VarType.PinSubCategoryObject = TWeakObjectPtr<UScriptStruct>(TBaseStructure<FVector>::Get());
        }
        else if (VariableType == TEXT("Rotator"))
        {
            NewVar.VarType.PinCategory = UEdGraphSchema_K2::PC_Struct;
            // REAL UE 5.6 IMPLEMENTATION: Use proper struct reference
            NewVar.VarType.PinSubCategoryObject = TWeakObjectPtr<UScriptStruct>(TBaseStructure<FRotator>::Get());
        }
        else if (VariableType == TEXT("Transform"))
        {
            NewVar.VarType.PinCategory = UEdGraphSchema_K2::PC_Struct;
            // REAL UE 5.6 IMPLEMENTATION: Use proper struct reference
            NewVar.VarType.PinSubCategoryObject = TWeakObjectPtr<UScriptStruct>(TBaseStructure<FTransform>::Get());
        }
        else
        {
            // Default to Object type and try to find the class
            NewVar.VarType.PinCategory = UEdGraphSchema_K2::PC_Object;
            if (const UClass* FoundClass = GetCachedClass(VariableType))
            {
                NewVar.VarType.PinSubCategoryObject = TWeakObjectPtr<UClass>(const_cast<UClass*>(FoundClass));
            }
        }

        // Set variable properties
        if (bIsExposed)
        {
            NewVar.PropertyFlags |= CPF_BlueprintVisible;
        }

        if (bIsPrivate)
        {
            NewVar.PropertyFlags |= CPF_DisableEditOnInstance;
        }
        else
        {
            NewVar.PropertyFlags |= CPF_Edit;
        }

        // Set default value if provided
        if (!DefaultValue.IsEmpty())
        {
            NewVar.DefaultValue = DefaultValue;
        }

        // REAL UE 5.6 IMPLEMENTATION: Add variable to blueprint using correct UE 5.6 API
        FBlueprintEditorUtils::AddMemberVariable(Blueprint, NewVar.VarName, NewVar.VarType, NewVar.DefaultValue);

        // Mark blueprint as modified
        FBlueprintEditorUtils::MarkBlueprintAsModified(Blueprint);

        // Save the blueprint
        UPackage* const BlueprintPackage = Blueprint->GetPackage();
        if (BlueprintPackage)
        {
            BlueprintPackage->MarkPackageDirty();
            const FString PackageFileName = FPackageName::LongPackageNameToFilename(BlueprintPackage->GetName(), FPackageName::GetAssetPackageExtension());
            FSavePackageArgs SaveArgs;
            SaveArgs.TopLevelFlags = RF_Standalone;
            SaveArgs.Error = GError;
            SaveArgs.bSlowTask = true;
            SaveArgs.bWarnOfLongFilename = true;
            UPackage::SavePackage(BlueprintPackage, Blueprint, *PackageFileName, SaveArgs);
        }

        TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
        ResultObj->SetStringField(TEXT("blueprint_name"), BlueprintName);
        ResultObj->SetStringField(TEXT("variable_name"), VariableName);
        ResultObj->SetStringField(TEXT("variable_type"), VariableType);
        ResultObj->SetBoolField(TEXT("is_exposed"), bIsExposed);
        ResultObj->SetBoolField(TEXT("is_private"), bIsPrivate);
        ResultObj->SetStringField(TEXT("default_value"), DefaultValue);
        return FUnrealMCPCommonUtils::CreateSuccessResponse(ResultObj);
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Exception adding Blueprint variable: %s"), UTF8_TO_TCHAR(e.what()));
        return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMsg);
    }
    catch (...)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Unknown exception adding Blueprint variable"));
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to add variable to Blueprint"));
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleGetBlueprintInfo(const TSharedPtr<FJsonObject>& Params)
{
    // Validate required parameters
    const TArray<FString> RequiredFields = {TEXT("blueprint_name")};
    if (const auto ValidationError = ValidateRequiredParams(Params, RequiredFields))
    {
        return ValidationError;
    }

    const FString BlueprintName = Params->GetStringField(TEXT("blueprint_name"));

    // Find the blueprint
    UBlueprint* Blueprint = FUnrealMCPCommonUtils::FindBlueprint(BlueprintName);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    try
    {
        TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();

        // Basic blueprint information
        ResultObj->SetStringField(TEXT("name"), Blueprint->GetName());
        ResultObj->SetStringField(TEXT("path"), Blueprint->GetPackage()->GetName());
        ResultObj->SetStringField(TEXT("parent_class"), Blueprint->ParentClass ? Blueprint->ParentClass->GetName() : TEXT("None"));
        ResultObj->SetStringField(TEXT("blueprint_type"), Blueprint->BlueprintType == BPTYPE_Normal ? TEXT("Normal") :
                                                          Blueprint->BlueprintType == BPTYPE_Interface ? TEXT("Interface") :
                                                          Blueprint->BlueprintType == BPTYPE_MacroLibrary ? TEXT("MacroLibrary") : TEXT("Unknown"));

        // Variables information
        TArray<TSharedPtr<FJsonValue>> VariablesArray;
        for (const FBPVariableDescription& Variable : Blueprint->NewVariables)
        {
            TSharedPtr<FJsonObject> VarObj = MakeShared<FJsonObject>();
            VarObj->SetStringField(TEXT("name"), Variable.VarName.ToString());
            VarObj->SetStringField(TEXT("type"), Variable.VarType.PinCategory.ToString());
            VarObj->SetStringField(TEXT("friendly_name"), Variable.FriendlyName);
            VarObj->SetBoolField(TEXT("is_exposed"), (Variable.PropertyFlags & CPF_BlueprintVisible) != 0);
            VarObj->SetBoolField(TEXT("is_editable"), (Variable.PropertyFlags & CPF_Edit) != 0);
            VarObj->SetStringField(TEXT("default_value"), Variable.DefaultValue);
            VariablesArray.Add(MakeShared<FJsonValueObject>(VarObj));
        }
        ResultObj->SetArrayField(TEXT("variables"), VariablesArray);

        // Functions information
        TArray<TSharedPtr<FJsonValue>> FunctionsArray;
        for (UEdGraph* Graph : Blueprint->FunctionGraphs)
        {
            if (Graph)
            {
                TSharedPtr<FJsonObject> FuncObj = MakeShared<FJsonObject>();
                FuncObj->SetStringField(TEXT("name"), Graph->GetName());
                FuncObj->SetNumberField(TEXT("nodes_count"), Graph->Nodes.Num());
                FunctionsArray.Add(MakeShared<FJsonValueObject>(FuncObj));
            }
        }
        ResultObj->SetArrayField(TEXT("functions"), FunctionsArray);

        // Components information
        TArray<TSharedPtr<FJsonValue>> ComponentsArray;
        if (Blueprint->SimpleConstructionScript)
        {
            for (USCS_Node* Node : Blueprint->SimpleConstructionScript->GetAllNodes())
            {
                if (Node && Node->ComponentTemplate)
                {
                    TSharedPtr<FJsonObject> CompObj = MakeShared<FJsonObject>();
                    CompObj->SetStringField(TEXT("name"), Node->GetVariableName().ToString());
                    CompObj->SetStringField(TEXT("class"), Node->ComponentTemplate->GetClass()->GetName());
                    ComponentsArray.Add(MakeShared<FJsonValueObject>(CompObj));
                }
            }
        }
        ResultObj->SetArrayField(TEXT("components"), ComponentsArray);

        // Graphs information
        TArray<TSharedPtr<FJsonValue>> GraphsArray;
        for (UEdGraph* Graph : Blueprint->UbergraphPages)
        {
            if (Graph)
            {
                TSharedPtr<FJsonObject> GraphObj = MakeShared<FJsonObject>();
                GraphObj->SetStringField(TEXT("name"), Graph->GetName());
                GraphObj->SetNumberField(TEXT("nodes_count"), Graph->Nodes.Num());
                GraphsArray.Add(MakeShared<FJsonValueObject>(GraphObj));
            }
        }
        ResultObj->SetArrayField(TEXT("event_graphs"), GraphsArray);

        // Compilation status
        ResultObj->SetBoolField(TEXT("is_compiled"), Blueprint->Status == BS_UpToDate);
        ResultObj->SetStringField(TEXT("compilation_status"),
            Blueprint->Status == BS_UpToDate ? TEXT("UpToDate") :
            Blueprint->Status == BS_Dirty ? TEXT("Dirty") :
            Blueprint->Status == BS_Error ? TEXT("Error") : TEXT("Unknown"));

        return FUnrealMCPCommonUtils::CreateSuccessResponse(ResultObj);
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Exception getting Blueprint info: %s"), UTF8_TO_TCHAR(e.what()));
        return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMsg);
    }
    catch (...)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Unknown exception getting Blueprint info"));
    }
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleDeleteBlueprint(const TSharedPtr<FJsonObject>& Params)
{
    // Validate required parameters
    const TArray<FString> RequiredFields = {TEXT("blueprint_name")};
    if (const auto ValidationError = ValidateRequiredParams(Params, RequiredFields))
    {
        return ValidationError;
    }

    const FString BlueprintName = Params->GetStringField(TEXT("blueprint_name"));
    const FString PackagePath = TEXT("/Game/Blueprints/");
    const FString AssetPath = PackagePath + BlueprintName;

    // Check if blueprint exists
    if (!UEditorAssetLibrary::DoesAssetExist(AssetPath))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint does not exist: %s"), *BlueprintName));
    }

    try
    {
        // Get confirmation parameter (optional, defaults to false for safety)
        bool bConfirmDelete = false;
        Params->TryGetBoolField(TEXT("confirm_delete"), bConfirmDelete);

        if (!bConfirmDelete)
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Delete operation requires 'confirm_delete': true parameter for safety"));
        }

        // Delete the asset
        bool bDeleteSuccess = UEditorAssetLibrary::DeleteAsset(AssetPath);

        if (bDeleteSuccess)
        {
            UE_LOG(LogUnrealMCP, Log, TEXT("Successfully deleted Blueprint: %s"), *BlueprintName);

            TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
            ResultObj->SetStringField(TEXT("deleted_blueprint"), BlueprintName);
            ResultObj->SetStringField(TEXT("deleted_path"), AssetPath);
            ResultObj->SetBoolField(TEXT("success"), true);
            return FUnrealMCPCommonUtils::CreateSuccessResponse(ResultObj);
        }
        else
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Failed to delete Blueprint: %s"), *BlueprintName));
        }
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Exception deleting Blueprint: %s"), UTF8_TO_TCHAR(e.what()));
        return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMsg);
    }
    catch (...)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Unknown exception deleting Blueprint"));
    }
}

TSharedPtr<FJsonObject> FUnrealMCPBlueprintCommands::HandleDuplicateBlueprint(const TSharedPtr<FJsonObject>& Params)
{
    // Validate required parameters
    const TArray<FString> RequiredFields = {TEXT("source_blueprint_name"), TEXT("new_blueprint_name")};
    if (const auto ValidationError = ValidateRequiredParams(Params, RequiredFields))
    {
        return ValidationError;
    }

    const FString SourceBlueprintName = Params->GetStringField(TEXT("source_blueprint_name"));
    const FString NewBlueprintName = Params->GetStringField(TEXT("new_blueprint_name"));
    const FString PackagePath = TEXT("/Game/Blueprints/");
    const FString SourceAssetPath = PackagePath + SourceBlueprintName;
    const FString NewAssetPath = PackagePath + NewBlueprintName;

    // Check if source blueprint exists
    if (!UEditorAssetLibrary::DoesAssetExist(SourceAssetPath))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Source Blueprint does not exist: %s"), *SourceBlueprintName));
    }

    // Check if target blueprint already exists
    if (UEditorAssetLibrary::DoesAssetExist(NewAssetPath))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Target Blueprint already exists: %s"), *NewBlueprintName));
    }

    try
    {
        // Duplicate the asset
        UObject* DuplicatedAsset = UEditorAssetLibrary::DuplicateAsset(SourceAssetPath, NewAssetPath);

        if (DuplicatedAsset)
        {
            UBlueprint* DuplicatedBlueprint = Cast<UBlueprint>(DuplicatedAsset);
            if (DuplicatedBlueprint)
            {
                // Rename the duplicated blueprint
                DuplicatedBlueprint->Rename(*NewBlueprintName);

                // Mark package dirty and save
                UPackage* Package = DuplicatedBlueprint->GetPackage();
                if (Package)
                {
                    Package->MarkPackageDirty();

                    const FString PackageFileName = FPackageName::LongPackageNameToFilename(Package->GetName(), FPackageName::GetAssetPackageExtension());
                    FSavePackageArgs SaveArgs;
                    SaveArgs.TopLevelFlags = RF_Standalone;
                    SaveArgs.Error = GError;
                    SaveArgs.bSlowTask = true;
                    SaveArgs.bWarnOfLongFilename = true;
                    UPackage::SavePackage(Package, DuplicatedBlueprint, *PackageFileName, SaveArgs);
                }

                UE_LOG(LogUnrealMCP, Log, TEXT("Successfully duplicated Blueprint from '%s' to '%s'"), *SourceBlueprintName, *NewBlueprintName);

                TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
                ResultObj->SetStringField(TEXT("source_blueprint"), SourceBlueprintName);
                ResultObj->SetStringField(TEXT("new_blueprint"), NewBlueprintName);
                ResultObj->SetStringField(TEXT("source_path"), SourceAssetPath);
                ResultObj->SetStringField(TEXT("new_path"), NewAssetPath);
                ResultObj->SetStringField(TEXT("parent_class"), DuplicatedBlueprint->ParentClass ? DuplicatedBlueprint->ParentClass->GetName() : TEXT("None"));
                ResultObj->SetBoolField(TEXT("success"), true);
                return FUnrealMCPCommonUtils::CreateSuccessResponse(ResultObj);
            }
        }

        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Failed to duplicate Blueprint from '%s' to '%s'"), *SourceBlueprintName, *NewBlueprintName));
    }
    catch (const std::exception& e)
    {
        FString ErrorMsg = FString::Printf(TEXT("Exception duplicating Blueprint: %s"), UTF8_TO_TCHAR(e.what()));
        return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMsg);
    }
    catch (...)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Unknown exception duplicating Blueprint"));
    }
}