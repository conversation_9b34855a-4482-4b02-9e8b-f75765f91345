"""Substrate Material Tools for Unreal Engine 5.6.

This module provides tools for managing Substrate Materials in Unreal Engine 5.6.
Substrate is the next-generation material system that replaces the traditional
shading models with a more expressive and flexible framework.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_substrate_material_tools(mcp: FastMCP):
    """Register substrate material tools with the MCP server."""
    
    @mcp.tool()
    def create_substrate_material(
        ctx: Context,
        material_name: str,
        package_path: str = "/Game/Materials/Substrate/",
        substrate_type: str = "BSDF",
        base_color: List[float] = [1.0, 1.0, 1.0, 1.0],
        metallic: float = 0.0,
        roughness: float = 0.5,
        normal_intensity: float = 1.0
    ) -> Dict[str, Any]:
        """Create a new Substrate Material with modern UE 5.6 APIs.
        
        Args:
            material_name: Name for the new material
            package_path: Path where to create the material
            substrate_type: Type of substrate material (BSDF, Unlit, etc.)
            base_color: Base color as RGBA values [0.0-1.0]
            metallic: Metallic value [0.0-1.0]
            roughness: Roughness value [0.0-1.0]
            normal_intensity: Normal map intensity [0.0-2.0]
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("create_substrate_material", {
                "material_name": material_name,
                "package_path": package_path,
                "substrate_type": substrate_type,
                "base_color": base_color,
                "metallic": metallic,
                "roughness": roughness,
                "normal_intensity": normal_intensity
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Create Substrate Material response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error creating Substrate Material: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def configure_substrate_material(
        ctx: Context,
        material_name: str,
        properties: Dict[str, Any],
        update_existing_instances: bool = True
    ) -> Dict[str, Any]:
        """Configure Substrate Material properties using modern UE 5.6 APIs.
        
        Args:
            material_name: Name of the target material
            properties: Dictionary with material properties to configure
            update_existing_instances: Whether to update existing material instances
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("configure_substrate_material", {
                "material_name": material_name,
                "properties": properties,
                "update_existing_instances": update_existing_instances
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Configure Substrate Material response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error configuring Substrate Material: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def add_substrate_node(
        ctx: Context,
        material_name: str,
        node_type: str,
        node_properties: Dict[str, Any],
        position: List[int] = [0, 0]
    ) -> Dict[str, Any]:
        """Add Substrate nodes to a material graph.
        
        Args:
            material_name: Name of the target material
            node_type: Type of Substrate node (SubstrateBSDF, SubstrateUnlit, etc.)
            node_properties: Properties for the new node
            position: [X, Y] position in the material editor
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("add_substrate_node", {
                "material_name": material_name,
                "node_type": node_type,
                "node_properties": node_properties,
                "position": position
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Add Substrate Node response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error adding Substrate Node: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def convert_to_substrate_material(
        ctx: Context,
        source_material: str,
        target_material: str,
        preserve_original: bool = True,
        conversion_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Convert legacy material to Substrate Material.
        
        Args:
            source_material: Path to the legacy material
            target_material: Path for the new Substrate material
            preserve_original: Whether to keep the original material
            conversion_options: Additional conversion settings
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("convert_to_substrate_material", {
                "source_material": source_material,
                "target_material": target_material,
                "preserve_original": preserve_original,
                "conversion_options": conversion_options or {}
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Convert to Substrate Material response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error converting to Substrate Material: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def get_substrate_material_info(
        ctx: Context,
        material_name: str,
        include_node_graph: bool = False,
        include_performance_stats: bool = False
    ) -> Dict[str, Any]:
        """Get Substrate Material information and capabilities.
        
        Args:
            material_name: Name of the material to inspect
            include_node_graph: Include material node graph information
            include_performance_stats: Include performance statistics
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("get_substrate_material_info", {
                "material_name": material_name,
                "include_node_graph": include_node_graph,
                "include_performance_stats": include_performance_stats
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Get Substrate Material Info response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error getting Substrate Material info: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def toggle_substrate_system(
        ctx: Context,
        enabled: bool,
        restart_required_warning: bool = True
    ) -> Dict[str, Any]:
        """Enable/Disable Substrate Materials system in the project.
        
        Args:
            enabled: Boolean to enable/disable Substrate
            restart_required_warning: Show warning about restart requirement
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("toggle_substrate_system", {
                "enabled": enabled,
                "restart_required_warning": restart_required_warning
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Toggle Substrate System response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error toggling Substrate System: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def create_substrate_material_instance(
        ctx: Context,
        parent_material: str,
        instance_name: str,
        package_path: str = "/Game/Materials/Instances/",
        parameter_overrides: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a Substrate Material Instance.
        
        Args:
            parent_material: Path to the parent Substrate material
            instance_name: Name for the new material instance
            package_path: Path where to create the instance
            parameter_overrides: Dictionary of parameter values to override
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("create_substrate_material_instance", {
                "parent_material": parent_material,
                "instance_name": instance_name,
                "package_path": package_path,
                "parameter_overrides": parameter_overrides or {}
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Create Substrate Material Instance response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error creating Substrate Material Instance: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def optimize_substrate_material(
        ctx: Context,
        material_name: str,
        optimization_level: str = "Medium",
        target_platform: str = "Desktop",
        preserve_quality: bool = True
    ) -> Dict[str, Any]:
        """Optimize Substrate Material for performance.
        
        Args:
            material_name: Name of the material to optimize
            optimization_level: Level of optimization (Low, Medium, High, Aggressive)
            target_platform: Target platform (Desktop, Console, Mobile)
            preserve_quality: Whether to preserve visual quality during optimization
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("optimize_substrate_material", {
                "material_name": material_name,
                "optimization_level": optimization_level,
                "target_platform": target_platform,
                "preserve_quality": preserve_quality
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Optimize Substrate Material response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error optimizing Substrate Material: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def validate_substrate_material(
        ctx: Context,
        material_name: str,
        check_performance: bool = True,
        check_compatibility: bool = True,
        target_platforms: List[str] = ["Desktop"]
    ) -> Dict[str, Any]:
        """Validate Substrate Material for errors and performance issues.
        
        Args:
            material_name: Name of the material to validate
            check_performance: Whether to check for performance issues
            check_compatibility: Whether to check platform compatibility
            target_platforms: List of target platforms to validate against
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("validate_substrate_material", {
                "material_name": material_name,
                "check_performance": check_performance,
                "check_compatibility": check_compatibility,
                "target_platforms": target_platforms
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Validate Substrate Material response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error validating Substrate Material: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def get_substrate_system_status(ctx: Context) -> Dict[str, Any]:
        """Get current status of the Substrate Material system.
        
        Returns information about Substrate system state, capabilities,
        and configuration.
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("get_substrate_system_status", {})
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Get Substrate System Status response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error getting Substrate System status: {e}")
            return {"error": str(e)}