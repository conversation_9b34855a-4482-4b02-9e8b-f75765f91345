"""
Data Layer Tools for Unreal MCP.

This module provides tools for managing Data Layers in Unreal Engine 5.6.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_data_layer_tools(mcp: FastMCP):
    """Register data layer tools with the MCP server."""
    
    @mcp.tool()
    def create_data_layer_asset(
        ctx: Context,
        layer_name: str,
        layer_type: str = "Runtime",
        debug_color: str = "",
        package_path: str = "/Game/DataLayers/"
    ) -> Dict[str, Any]:
        """Create a new Data Layer Asset (.uasset file).
        
        Args:
            layer_name: Name of the Data Layer
            layer_type: Runtime or Editor layer type
            debug_color: Color for visualization (optional)
            package_path: Where to save the asset
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("create_data_layer_asset", {
                "layer_name": layer_name,
                "layer_type": layer_type,
                "debug_color": debug_color,
                "package_path": package_path
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Data Layer Asset creation response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error creating Data Layer Asset: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def create_data_layer_assets_batch(
        ctx: Context,
        layer_names: List[str],
        layer_type: str = "Runtime",
        package_path: str = "/Game/DataLayers/"
    ) -> Dict[str, Any]:
        """Create multiple Data Layer Assets for large projects.
        
        Args:
            layer_names: Array of layer names to create
            layer_type: Type for all layers
            package_path: Base path for assets
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("create_data_layer_assets_batch", {
                "layer_names": layer_names,
                "layer_type": layer_type,
                "package_path": package_path
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Batch Data Layer Assets creation response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error creating Data Layer Assets batch: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def create_data_layer_instance(
        ctx: Context,
        data_layer_asset_path: str,
        is_visible: bool = True,
        is_loaded: bool = True
    ) -> Dict[str, Any]:
        """Create a Data Layer Instance in the current world.
        
        Args:
            data_layer_asset_path: Path to the Data Layer Asset
            is_visible: Initial visibility state
            is_loaded: Initial loaded state
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("create_data_layer_instance", {
                "data_layer_asset_path": data_layer_asset_path,
                "is_visible": is_visible,
                "is_loaded": is_loaded
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Data Layer Instance creation response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error creating Data Layer Instance: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def add_actors_to_data_layer(
        ctx: Context,
        actor_names: List[str],
        data_layer_asset_name: str,
        replace_existing: bool = False
    ) -> Dict[str, Any]:
        """Add actors to a Data Layer (robust batch operation).
        
        Args:
            actor_names: Array of actor names to add
            data_layer_asset_name: Target Data Layer
            replace_existing: Replace existing Data Layer assignments
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("add_actors_to_data_layer", {
                "actor_names": actor_names,
                "data_layer_asset_name": data_layer_asset_name,
                "replace_existing": replace_existing
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Add actors to Data Layer response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error adding actors to Data Layer: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def remove_actors_from_data_layer(
        ctx: Context,
        actor_names: List[str],
        data_layer_asset_name: str
    ) -> Dict[str, Any]:
        """Remove actors from a Data Layer.
        
        Args:
            actor_names: Array of actor names to remove
            data_layer_asset_name: Source Data Layer
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("remove_actors_from_data_layer", {
                "actor_names": actor_names,
                "data_layer_asset_name": data_layer_asset_name
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Remove actors from Data Layer response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error removing actors from Data Layer: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def organize_actors_into_data_layers(
        ctx: Context,
        actor_names: List[str],
        organization_strategy: str = "ByType",
        custom_rules: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Organize 400+ assets into Data Layers automatically.
        
        Args:
            actor_names: All actors to organize
            organization_strategy: "ByType", "ByLocation", "ByName", "Custom"
            custom_rules: JSON object with custom organization rules
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("organize_actors_into_data_layers", {
                "actor_names": actor_names,
                "organization_strategy": organization_strategy,
                "custom_rules": custom_rules
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Organize actors into Data Layers response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error organizing actors into Data Layers: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def set_data_layer_visibility(
        ctx: Context,
        data_layer_asset_name: str,
        is_visible: bool
    ) -> Dict[str, Any]:
        """Set Data Layer visibility state.
        
        Args:
            data_layer_asset_name: Target Data Layer
            is_visible: New visibility state
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("set_data_layer_visibility", {
                "data_layer_asset_name": data_layer_asset_name,
                "is_visible": is_visible
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Set Data Layer visibility response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error setting Data Layer visibility: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def set_data_layer_runtime_state(
        ctx: Context,
        data_layer_asset_name: str,
        runtime_state: str
    ) -> Dict[str, Any]:
        """Set Data Layer runtime state (Unloaded/Loaded/Activated).
        
        Args:
            data_layer_asset_name: Target Data Layer
            runtime_state: "Unloaded", "Loaded", "Activated"
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("set_data_layer_runtime_state", {
                "data_layer_asset_name": data_layer_asset_name,
                "runtime_state": runtime_state
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Set Data Layer runtime state response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error setting Data Layer runtime state: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def get_all_data_layers(
        ctx: Context,
        include_editor_only: bool = False
    ) -> Dict[str, Any]:
        """Get all Data Layers in the current world.

        Args:
            include_editor_only: Include editor-only layers
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}

            response = unreal.send_command("get_all_data_layers", {
                "include_editor_only": include_editor_only
            })

            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}

            logger.info(f"Get all Data Layers response: {response}")
            return response.get("result", response)

        except Exception as e:
            logger.error(f"Error getting all Data Layers: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def get_actors_in_data_layer(
        ctx: Context,
        data_layer_asset_name: str,
        include_hidden: bool = False
    ) -> Dict[str, Any]:
        """Get actors in a specific Data Layer.

        Args:
            data_layer_asset_name: Target Data Layer
            include_hidden: Include hidden actors
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}

            response = unreal.send_command("get_actors_in_data_layer", {
                "data_layer_asset_name": data_layer_asset_name,
                "include_hidden": include_hidden
            })

            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}

            logger.info(f"Get actors in Data Layer response: {response}")
            return response.get("result", response)

        except Exception as e:
            logger.error(f"Error getting actors in Data Layer: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def get_actor_data_layers(
        ctx: Context,
        actor_name: str
    ) -> Dict[str, Any]:
        """Get Data Layer information for an actor.

        Args:
            actor_name: Target actor
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}

            response = unreal.send_command("get_actor_data_layers", {
                "actor_name": actor_name
            })

            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}

            logger.info(f"Get actor Data Layers response: {response}")
            return response.get("result", response)

        except Exception as e:
            logger.error(f"Error getting actor Data Layers: {e}")
            return {"error": str(e)}
