"""Fast Geometry Streaming tools for Unreal Engine 5.6.

This module provides tools for managing Fast Geometry Streaming in Unreal Engine 5.6,
including enabling/disabling the system, configuring streaming parameters, managing
transformers, and retrieving performance metrics.

Based on the actual UE 5.6 FastGeoStreaming plugin source code.
"""

import logging
from typing import Dict, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_fast_geo_streaming_tools(mcp: FastMCP):
    """Register Fast Geometry Streaming tools with the MCP server."""
    
    @mcp.tool()
    def enable_fast_geo_streaming(
        ctx: Context,
        enable: bool = True,
        show_debug: bool = False
    ) -> Dict[str, Any]:
        """Enable or disable Fast Geometry Streaming in Unreal Engine 5.6.
        
        This tool uses the actual FastGeo console variables from UE 5.6 source:
        - FastGeo.Enable: Main FastGeo system toggle
        - FastGeo.Show: FastGeo visualization toggle
        - FastGeo.EnableTransformerDebugMode: Debug mode
        
        Args:
            enable: Whether to enable (True) or disable (False) Fast Geometry Streaming
            show_debug: Enable FastGeo visualization and debug mode
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("fast_geo_streaming", {
                "action": "enable",
                "enable": enable,
                "show_debug": show_debug
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Enable Fast Geo Streaming response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error enabling Fast Geo Streaming: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def configure_fast_geo_streaming(
        ctx: Context,
        time_budget_ms: Optional[float] = None,
        max_components_to_process: Optional[int] = None,
        parallel_worker_count: Optional[int] = None,
        debug_mode: bool = False
    ) -> Dict[str, Any]:
        """Configure Fast Geometry Streaming parameters using actual UE 5.6 console variables.
        
        This tool configures the AsyncRenderStateTask parameters from the UE 5.6 source:
        - FastGeo.AsyncRenderStateTask.TimeBudgetMS: Maximum time budget for async tasks
        - FastGeo.AsyncRenderStateTask.MaxNumComponentsToProcess: Max components per frame
        - FastGeo.AsyncRenderStateTask.ParallelWorkerCount: Number of parallel workers
        - FastGeo.EnableTransformerDebugMode: Enable debug visualization
        
        Args:
            time_budget_ms: Maximum time budget in milliseconds for async render state tasks
            max_components_to_process: Maximum number of components to process per frame
            parallel_worker_count: Number of parallel workers for async tasks
            debug_mode: Enable FastGeo transformer debug mode
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            config_data = {"debug_mode": debug_mode}
            if time_budget_ms is not None:
                config_data["time_budget_ms"] = time_budget_ms
            if max_components_to_process is not None:
                config_data["max_components_to_process"] = max_components_to_process
            if parallel_worker_count is not None:
                config_data["parallel_worker_count"] = parallel_worker_count
                
            response = unreal.send_command("fast_geo_streaming", {
                "action": "configure",
                **config_data
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Configure Fast Geo Streaming response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error configuring Fast Geo Streaming: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def add_fast_geo_transformer(
        ctx: Context,
        transformer_type: str = "FastGeoWorldPartitionRuntimeCellTransformer",
        debug_mode: bool = False
    ) -> Dict[str, Any]:
        """Add and configure a Fast Geometry Streaming transformer.
        
        This tool configures the FastGeoWorldPartitionRuntimeCellTransformer which is
        automatically used when FastGeo is enabled with World Partition in UE 5.6.
        
        Args:
            transformer_type: Type of transformer to add
            debug_mode: Enable debug mode for the transformer
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("fast_geo_streaming", {
                "action": "add_transformer",
                "transformer_type": transformer_type,
                "debug_mode": debug_mode
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Add Fast Geo Transformer response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error adding Fast Geo Transformer: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def get_fast_geo_metrics(
        ctx: Context,
        include_detailed_stats: bool = False
    ) -> Dict[str, Any]:
        """Get Fast Geometry Streaming performance metrics and current configuration.
        
        This tool retrieves real-time metrics from the FastGeoWorldSubsystem and
        current console variable values from the UE 5.6 engine.
        
        Args:
            include_detailed_stats: Include detailed performance statistics
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("fast_geo_streaming", {
                "action": "get_metrics",
                "include_detailed_stats": include_detailed_stats
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Get Fast Geo Metrics response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error getting Fast Geo Metrics: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def get_fast_geo_status(ctx: Context) -> Dict[str, Any]:
        """Get the current status of Fast Geometry Streaming system.
        
        This tool checks if FastGeo is enabled and provides system status information.
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("fast_geo_streaming", {
                "action": "get_status"
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Get Fast Geo Status response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error getting Fast Geo Status: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def toggle_fast_geo_system(
        ctx: Context,
        force_state: Optional[bool] = None
    ) -> Dict[str, Any]:
        """Toggle the Fast Geometry Streaming system on/off or force a specific state.
        
        This tool provides a quick way to toggle FastGeo or force it to a specific state.
        
        Args:
            force_state: If provided, forces FastGeo to this state instead of toggling
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            command_data = {"action": "toggle_system"}
            if force_state is not None:
                command_data["force_state"] = force_state
                
            response = unreal.send_command("fast_geo_streaming", command_data)
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Toggle Fast Geo System response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error toggling Fast Geo System: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def optimize_fast_geo_for_large_world(
        ctx: Context,
        asset_count: int,
        target_frame_rate: int = 60,
        platform_type: str = "Desktop"
    ) -> Dict[str, Any]:
        """Apply optimizations for Fast Geometry Streaming in large worlds.
        
        This tool configures FastGeo settings optimized for large world scenarios,
        based on UE 5.6 best practices.
        
        Args:
            asset_count: Estimated number of assets in the world
            target_frame_rate: Target frame rate (30, 60, 120)
            platform_type: "Desktop", "Console", "Mobile"
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("fast_geo_streaming", {
                "action": "optimize_for_large_world",
                "asset_count": asset_count,
                "target_frame_rate": target_frame_rate,
                "platform_type": platform_type
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Optimize Fast Geo for Large World response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error optimizing Fast Geo for Large World: {e}")
            return {"error": str(e)}