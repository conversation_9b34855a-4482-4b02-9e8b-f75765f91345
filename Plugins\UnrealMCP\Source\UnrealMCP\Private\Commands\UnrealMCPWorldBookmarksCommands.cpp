#include "Commands/UnrealMCPWorldBookmarksCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Editor.h"
#include "EditorViewportClient.h"
#include "LevelEditorViewport.h"
#include "HAL/FileManager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

FUnrealMCPWorldBookmarksCommands::FUnrealMCPWorldBookmarksCommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPWorldBookmarksCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandType == TEXT("create_world_bookmark"))
    {
        return HandleCreateWorldBookmark(Params);
    }
    else if (CommandType == TEXT("navigate_to_bookmark"))
    {
        return HandleNavigateToBookmark(Params);
    }
    else if (CommandType == TEXT("list_world_bookmarks"))
    {
        return HandleListWorldBookmarks(Params);
    }
    else if (CommandType == TEXT("update_world_bookmark"))
    {
        return HandleUpdateWorldBookmark(Params);
    }
    else if (CommandType == TEXT("delete_world_bookmark"))
    {
        return HandleDeleteWorldBookmark(Params);
    }
    else if (CommandType == TEXT("import_bookmarks"))
    {
        return HandleImportBookmarks(Params);
    }
    else if (CommandType == TEXT("export_bookmarks"))
    {
        return HandleExportBookmarks(Params);
    }
    else if (CommandType == TEXT("create_bookmark_sequence"))
    {
        return HandleCreateBookmarkSequence(Params);
    }
    else if (CommandType == TEXT("play_bookmark_sequence"))
    {
        return HandlePlayBookmarkSequence(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown World Bookmarks command: %s"), *CommandType));
}

// REAL UE 5.6 PRODUCTION READY IMPLEMENTATION - Simplified implementations for missing methods
TSharedPtr<FJsonObject> FUnrealMCPWorldBookmarksCommands::HandleUpdateWorldBookmark(const TSharedPtr<FJsonObject>& Params)
{
    // Validate required parameters
    FString BookmarkName;
    if (!Params->TryGetStringField(TEXT("bookmark_name"), BookmarkName) || BookmarkName.IsEmpty())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or empty 'bookmark_name' parameter"));
    }

    // Validate bookmark name
    if (!ValidateBookmarkName(BookmarkName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Invalid bookmark name: %s"), *BookmarkName));
    }

    // Get current world
    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No active world found"));
    }

    // Load existing bookmarks
    TArray<FWorldBookmark> Bookmarks = LoadBookmarks();
    
    // Find the bookmark to update
    FString CurrentWorldName = GetCurrentWorldName();
    int32 BookmarkIndex = -1;
    for (int32 i = 0; i < Bookmarks.Num(); ++i)
    {
        if (Bookmarks[i].Name == BookmarkName && Bookmarks[i].WorldName == CurrentWorldName)
        {
            BookmarkIndex = i;
            break;
        }
    }

    if (BookmarkIndex == -1)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Bookmark '%s' not found in current world"), *BookmarkName));
    }

    // Update bookmark properties
    FWorldBookmark& Bookmark = Bookmarks[BookmarkIndex];
    
    // Update description if provided
    FString NewDescription;
    if (Params->TryGetStringField(TEXT("description"), NewDescription))
    {
        Bookmark.Description = NewDescription;
    }

    // Update location if provided
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj) && LocationObj->IsValid())
    {
        double X, Y, Z;
        if ((*LocationObj)->TryGetNumberField(TEXT("x"), X) &&
            (*LocationObj)->TryGetNumberField(TEXT("y"), Y) &&
            (*LocationObj)->TryGetNumberField(TEXT("z"), Z))
        {
            Bookmark.Location = FVector(X, Y, Z);
        }
    }
    else
    {
        // Use current camera location if not specified
        Bookmark.Location = GetCurrentCameraLocation();
    }

    // Update rotation if provided
    const TSharedPtr<FJsonObject>* RotationObj;
    if (Params->TryGetObjectField(TEXT("rotation"), RotationObj) && RotationObj->IsValid())
    {
        double Pitch, Yaw, Roll;
        if ((*RotationObj)->TryGetNumberField(TEXT("pitch"), Pitch) &&
            (*RotationObj)->TryGetNumberField(TEXT("yaw"), Yaw) &&
            (*RotationObj)->TryGetNumberField(TEXT("roll"), Roll))
        {
            Bookmark.Rotation = FRotator(Pitch, Yaw, Roll);
        }
    }
    else
    {
        // Use current camera rotation if not specified
        Bookmark.Rotation = GetCurrentCameraRotation();
    }

    // Update timestamp
    Bookmark.LastModified = FDateTime::Now();

    // Save updated bookmarks
    if (!SaveBookmarks(Bookmarks))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save updated bookmark"));
    }

    // Create success response
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("status"), TEXT("success"));
    ResultObj->SetStringField(TEXT("message"), FString::Printf(TEXT("Bookmark '%s' updated successfully"), *BookmarkName));
    
    // Add updated bookmark details
    TSharedPtr<FJsonObject> BookmarkObj = MakeShared<FJsonObject>();
    BookmarkObj->SetStringField(TEXT("name"), Bookmark.Name);
    BookmarkObj->SetStringField(TEXT("description"), Bookmark.Description);
    BookmarkObj->SetStringField(TEXT("world_name"), Bookmark.WorldName);
    BookmarkObj->SetStringField(TEXT("created_time"), Bookmark.CreatedTime.ToString());
    BookmarkObj->SetStringField(TEXT("last_modified"), Bookmark.LastModified.ToString());
    
    TSharedPtr<FJsonObject> LocationObjResult = MakeShared<FJsonObject>();
    LocationObjResult->SetNumberField(TEXT("x"), Bookmark.Location.X);
    LocationObjResult->SetNumberField(TEXT("y"), Bookmark.Location.Y);
    LocationObjResult->SetNumberField(TEXT("z"), Bookmark.Location.Z);
    BookmarkObj->SetObjectField(TEXT("location"), LocationObjResult);
    
    TSharedPtr<FJsonObject> RotationObjResult = MakeShared<FJsonObject>();
    RotationObjResult->SetNumberField(TEXT("pitch"), Bookmark.Rotation.Pitch);
    RotationObjResult->SetNumberField(TEXT("yaw"), Bookmark.Rotation.Yaw);
    RotationObjResult->SetNumberField(TEXT("roll"), Bookmark.Rotation.Roll);
    BookmarkObj->SetObjectField(TEXT("rotation"), RotationObjResult);
    
    ResultObj->SetObjectField(TEXT("bookmark"), BookmarkObj);

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldBookmarksCommands::HandleDeleteWorldBookmark(const TSharedPtr<FJsonObject>& Params)
{
	// Validate required parameters
	FString BookmarkName;
	if (!Params->TryGetStringField(TEXT("bookmark_name"), BookmarkName) || BookmarkName.IsEmpty())
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or empty 'bookmark_name' parameter"));
	}

	// Validate bookmark name
	if (!ValidateBookmarkName(BookmarkName))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Invalid bookmark name: %s"), *BookmarkName));
	}

	// Get current world
	UWorld* World = GetCurrentWorld();
	if (!World)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No active world found"));
	}

	// Load existing bookmarks
	TArray<FWorldBookmark> Bookmarks = LoadBookmarks();
	
	// Find the bookmark to delete
	FString CurrentWorldName = GetCurrentWorldName();
	int32 BookmarkIndex = -1;
	for (int32 i = 0; i < Bookmarks.Num(); ++i)
	{
		if (Bookmarks[i].Name == BookmarkName && Bookmarks[i].WorldName == CurrentWorldName)
		{
			BookmarkIndex = i;
			break;
		}
	}

	if (BookmarkIndex == -1)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Bookmark '%s' not found in current world"), *BookmarkName));
	}

	// Store bookmark info for response before deletion
	FWorldBookmark DeletedBookmark = Bookmarks[BookmarkIndex];

	// Remove the bookmark
	Bookmarks.RemoveAt(BookmarkIndex);

	// Save updated bookmarks
	if (!SaveBookmarks(Bookmarks))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save bookmarks after deletion"));
	}

	// Create success response
	TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
	ResultObj->SetStringField(TEXT("status"), TEXT("success"));
	ResultObj->SetStringField(TEXT("message"), FString::Printf(TEXT("Bookmark '%s' deleted successfully"), *BookmarkName));
	
	// Add deleted bookmark details
	TSharedPtr<FJsonObject> BookmarkObj = MakeShared<FJsonObject>();
	BookmarkObj->SetStringField(TEXT("name"), DeletedBookmark.Name);
	BookmarkObj->SetStringField(TEXT("description"), DeletedBookmark.Description);
	BookmarkObj->SetStringField(TEXT("world_name"), DeletedBookmark.WorldName);
	BookmarkObj->SetStringField(TEXT("created_time"), DeletedBookmark.CreatedTime.ToString());
	BookmarkObj->SetStringField(TEXT("last_modified"), DeletedBookmark.LastModified.ToString());
	
	TSharedPtr<FJsonObject> LocationObj = MakeShared<FJsonObject>();
	LocationObj->SetNumberField(TEXT("x"), DeletedBookmark.Location.X);
	LocationObj->SetNumberField(TEXT("y"), DeletedBookmark.Location.Y);
	LocationObj->SetNumberField(TEXT("z"), DeletedBookmark.Location.Z);
	BookmarkObj->SetObjectField(TEXT("location"), LocationObj);
	
	TSharedPtr<FJsonObject> RotationObj = MakeShared<FJsonObject>();
	RotationObj->SetNumberField(TEXT("pitch"), DeletedBookmark.Rotation.Pitch);
	RotationObj->SetNumberField(TEXT("yaw"), DeletedBookmark.Rotation.Yaw);
	RotationObj->SetNumberField(TEXT("roll"), DeletedBookmark.Rotation.Roll);
	BookmarkObj->SetObjectField(TEXT("rotation"), RotationObj);
	
	ResultObj->SetObjectField(TEXT("deleted_bookmark"), BookmarkObj);
	ResultObj->SetNumberField(TEXT("remaining_bookmarks_count"), Bookmarks.Num());

	return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldBookmarksCommands::HandleImportBookmarks(const TSharedPtr<FJsonObject>& Params)
{
	// Validate required parameters
	FString FilePath;
	if (!Params->TryGetStringField(TEXT("file_path"), FilePath) || FilePath.IsEmpty())
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or empty 'file_path' parameter"));
	}

	// Check if file exists
	if (!FPaths::FileExists(FilePath))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("File not found: %s"), *FilePath));
	}

	// Read file content
	FString FileContent;
	if (!FFileHelper::LoadFileToString(FileContent, *FilePath))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Failed to read file: %s"), *FilePath));
	}

	// Parse JSON
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(FileContent);
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid JSON format in import file"));
	}

	// Get bookmarks array from JSON
	const TArray<TSharedPtr<FJsonValue>>* BookmarksArray;
	if (!JsonObject->TryGetArrayField(TEXT("bookmarks"), BookmarksArray))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'bookmarks' array in import file"));
	}

	// Load existing bookmarks
	TArray<FWorldBookmark> ExistingBookmarks = LoadBookmarks();
	FString CurrentWorldName = GetCurrentWorldName();

	// Parse and validate imported bookmarks
	TArray<FWorldBookmark> ImportedBookmarks;
	TArray<FString> SkippedBookmarks;
	TArray<FString> UpdatedBookmarks;

	for (const TSharedPtr<FJsonValue>& BookmarkValue : *BookmarksArray)
	{
		const TSharedPtr<FJsonObject>* BookmarkObj;
		if (!BookmarkValue->TryGetObject(BookmarkObj) || !BookmarkObj->IsValid())
		{
			continue;
		}

		FWorldBookmark ImportedBookmark;
		
		// Parse bookmark fields
		if (!(*BookmarkObj)->TryGetStringField(TEXT("name"), ImportedBookmark.Name) || ImportedBookmark.Name.IsEmpty())
		{
			continue;
		}

		// Validate bookmark name
		if (!ValidateBookmarkName(ImportedBookmark.Name))
		{
			SkippedBookmarks.Add(ImportedBookmark.Name + TEXT(" (invalid name)"));
			continue;
		}

		(*BookmarkObj)->TryGetStringField(TEXT("description"), ImportedBookmark.Description);
		(*BookmarkObj)->TryGetStringField(TEXT("world_name"), ImportedBookmark.WorldName);

		// Parse location
		const TSharedPtr<FJsonObject>* LocationObj;
		if ((*BookmarkObj)->TryGetObjectField(TEXT("location"), LocationObj))
		{
			(*LocationObj)->TryGetNumberField(TEXT("x"), ImportedBookmark.Location.X);
			(*LocationObj)->TryGetNumberField(TEXT("y"), ImportedBookmark.Location.Y);
			(*LocationObj)->TryGetNumberField(TEXT("z"), ImportedBookmark.Location.Z);
		}

		// Parse rotation
		const TSharedPtr<FJsonObject>* RotationObj;
		if ((*BookmarkObj)->TryGetObjectField(TEXT("rotation"), RotationObj))
		{
			(*RotationObj)->TryGetNumberField(TEXT("pitch"), ImportedBookmark.Rotation.Pitch);
			(*RotationObj)->TryGetNumberField(TEXT("yaw"), ImportedBookmark.Rotation.Yaw);
			(*RotationObj)->TryGetNumberField(TEXT("roll"), ImportedBookmark.Rotation.Roll);
		}

		// Set timestamps
		FString CreatedTimeStr, LastModifiedStr;
		if ((*BookmarkObj)->TryGetStringField(TEXT("created_time"), CreatedTimeStr))
		{
			FDateTime::Parse(CreatedTimeStr, ImportedBookmark.CreatedTime);
		}
		else
		{
			ImportedBookmark.CreatedTime = FDateTime::Now();
		}

		if ((*BookmarkObj)->TryGetStringField(TEXT("last_modified"), LastModifiedStr))
		{
			FDateTime::Parse(LastModifiedStr, ImportedBookmark.LastModified);
		}
		else
		{
			ImportedBookmark.LastModified = FDateTime::Now();
		}

		// Check if bookmark already exists
		bool bBookmarkExists = false;
		for (int32 i = 0; i < ExistingBookmarks.Num(); ++i)
		{
			if (ExistingBookmarks[i].Name == ImportedBookmark.Name && ExistingBookmarks[i].WorldName == CurrentWorldName)
			{
				// Update existing bookmark
				ExistingBookmarks[i] = ImportedBookmark;
				ExistingBookmarks[i].WorldName = CurrentWorldName; // Ensure current world
				ExistingBookmarks[i].LastModified = FDateTime::Now();
				UpdatedBookmarks.Add(ImportedBookmark.Name);
				bBookmarkExists = true;
				break;
			}
		}

		if (!bBookmarkExists)
		{
			// Add new bookmark
			ImportedBookmark.WorldName = CurrentWorldName;
			ImportedBookmark.CreatedTime = FDateTime::Now();
			ImportedBookmark.LastModified = FDateTime::Now();
			ExistingBookmarks.Add(ImportedBookmark);
			ImportedBookmarks.Add(ImportedBookmark);
		}
	}

	// Save updated bookmarks
	if (!SaveBookmarks(ExistingBookmarks))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save imported bookmarks"));
	}

	// Create success response
	TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
	ResultObj->SetStringField(TEXT("status"), TEXT("success"));
	ResultObj->SetStringField(TEXT("message"), TEXT("Bookmarks imported successfully"));
	ResultObj->SetStringField(TEXT("file_path"), FilePath);
	ResultObj->SetNumberField(TEXT("imported_count"), ImportedBookmarks.Num());
	ResultObj->SetNumberField(TEXT("updated_count"), UpdatedBookmarks.Num());
	ResultObj->SetNumberField(TEXT("skipped_count"), SkippedBookmarks.Num());
	ResultObj->SetNumberField(TEXT("total_bookmarks"), ExistingBookmarks.Num());

	// Add arrays for detailed information
	TArray<TSharedPtr<FJsonValue>> ImportedArray;
	for (const FWorldBookmark& Bookmark : ImportedBookmarks)
	{
		TSharedPtr<FJsonObject> BookmarkObj = MakeShared<FJsonObject>();
		BookmarkObj->SetStringField(TEXT("name"), Bookmark.Name);
		BookmarkObj->SetStringField(TEXT("description"), Bookmark.Description);
		ImportedArray.Add(MakeShared<FJsonValueObject>(BookmarkObj));
	}
	ResultObj->SetArrayField(TEXT("imported_bookmarks"), ImportedArray);

	TArray<TSharedPtr<FJsonValue>> UpdatedArray;
	for (const FString& BookmarkName : UpdatedBookmarks)
	{
		UpdatedArray.Add(MakeShared<FJsonValueString>(BookmarkName));
	}
	ResultObj->SetArrayField(TEXT("updated_bookmarks"), UpdatedArray);

	TArray<TSharedPtr<FJsonValue>> SkippedArray;
	for (const FString& SkippedInfo : SkippedBookmarks)
	{
		SkippedArray.Add(MakeShared<FJsonValueString>(SkippedInfo));
	}
	ResultObj->SetArrayField(TEXT("skipped_bookmarks"), SkippedArray);

	return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldBookmarksCommands::HandleExportBookmarks(const TSharedPtr<FJsonObject>& Params)
{
	// Validate required parameters
	FString FilePath;
	if (!Params->TryGetStringField(TEXT("file_path"), FilePath) || FilePath.IsEmpty())
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or empty 'file_path' parameter"));
	}

	// Optional parameters
	bool bCurrentWorldOnly = true;
	Params->TryGetBoolField(TEXT("current_world_only"), bCurrentWorldOnly);

	bool bIncludeMetadata = true;
	Params->TryGetBoolField(TEXT("include_metadata"), bIncludeMetadata);

	// Load bookmarks
	TArray<FWorldBookmark> AllBookmarks = LoadBookmarks();
	FString CurrentWorldName = GetCurrentWorldName();

	// Filter bookmarks if needed
	TArray<FWorldBookmark> BookmarksToExport;
	if (bCurrentWorldOnly)
	{
		for (const FWorldBookmark& Bookmark : AllBookmarks)
		{
			if (Bookmark.WorldName == CurrentWorldName)
			{
				BookmarksToExport.Add(Bookmark);
			}
		}
	}
	else
	{
		BookmarksToExport = AllBookmarks;
	}

	if (BookmarksToExport.Num() == 0)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No bookmarks found to export"));
	}

	// Create JSON structure
	TSharedPtr<FJsonObject> ExportObj = MakeShared<FJsonObject>();
	
	// Add metadata if requested
	if (bIncludeMetadata)
	{
		TSharedPtr<FJsonObject> MetadataObj = MakeShared<FJsonObject>();
		MetadataObj->SetStringField(TEXT("export_time"), FDateTime::Now().ToString());
		MetadataObj->SetStringField(TEXT("unreal_version"), ENGINE_VERSION_STRING);
		MetadataObj->SetStringField(TEXT("current_world"), CurrentWorldName);
		MetadataObj->SetBoolField(TEXT("current_world_only"), bCurrentWorldOnly);
		MetadataObj->SetNumberField(TEXT("bookmark_count"), BookmarksToExport.Num());
		ExportObj->SetObjectField(TEXT("metadata"), MetadataObj);
	}

	// Add bookmarks array
	TArray<TSharedPtr<FJsonValue>> BookmarksArray;
	for (const FWorldBookmark& Bookmark : BookmarksToExport)
	{
		TSharedPtr<FJsonObject> BookmarkObj = MakeShared<FJsonObject>();
		BookmarkObj->SetStringField(TEXT("name"), Bookmark.Name);
		BookmarkObj->SetStringField(TEXT("description"), Bookmark.Description);
		BookmarkObj->SetStringField(TEXT("world_name"), Bookmark.WorldName);
		BookmarkObj->SetStringField(TEXT("created_time"), Bookmark.CreatedTime.ToString());
		BookmarkObj->SetStringField(TEXT("last_modified"), Bookmark.LastModified.ToString());
		
		// Add location
		TSharedPtr<FJsonObject> LocationObj = MakeShared<FJsonObject>();
		LocationObj->SetNumberField(TEXT("x"), Bookmark.Location.X);
		LocationObj->SetNumberField(TEXT("y"), Bookmark.Location.Y);
		LocationObj->SetNumberField(TEXT("z"), Bookmark.Location.Z);
		BookmarkObj->SetObjectField(TEXT("location"), LocationObj);
		
		// Add rotation
		TSharedPtr<FJsonObject> RotationObj = MakeShared<FJsonObject>();
		RotationObj->SetNumberField(TEXT("pitch"), Bookmark.Rotation.Pitch);
		RotationObj->SetNumberField(TEXT("yaw"), Bookmark.Rotation.Yaw);
		RotationObj->SetNumberField(TEXT("roll"), Bookmark.Rotation.Roll);
		BookmarkObj->SetObjectField(TEXT("rotation"), RotationObj);
		
		BookmarksArray.Add(MakeShared<FJsonValueObject>(BookmarkObj));
	}
	ExportObj->SetArrayField(TEXT("bookmarks"), BookmarksArray);

	// Convert to JSON string
	FString OutputString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
	// SetPrettyPrint is not available in UE 5.6 - using default formatting
	if (!FJsonSerializer::Serialize(ExportObj.ToSharedRef(), Writer))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to serialize bookmarks to JSON"));
	}

	// Ensure directory exists
	FString DirectoryPath = FPaths::GetPath(FilePath);
	if (!DirectoryPath.IsEmpty() && !FPaths::DirectoryExists(DirectoryPath))
	{
		if (!IFileManager::Get().MakeDirectory(*DirectoryPath, true))
		{
			return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Failed to create directory: %s"), *DirectoryPath));
		}
	}

	// Write to file
	if (!FFileHelper::SaveStringToFile(OutputString, *FilePath))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Failed to write to file: %s"), *FilePath));
	}

	// Create success response
	TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
	ResultObj->SetStringField(TEXT("status"), TEXT("success"));
	ResultObj->SetStringField(TEXT("message"), TEXT("Bookmarks exported successfully"));
	ResultObj->SetStringField(TEXT("file_path"), FilePath);
	ResultObj->SetNumberField(TEXT("exported_count"), BookmarksToExport.Num());
	ResultObj->SetBoolField(TEXT("current_world_only"), bCurrentWorldOnly);
	ResultObj->SetBoolField(TEXT("include_metadata"), bIncludeMetadata);
	ResultObj->SetStringField(TEXT("current_world"), CurrentWorldName);

	// Add file size info
	int64 FileSize = IFileManager::Get().FileSize(*FilePath);
	if (FileSize >= 0)
	{
		ResultObj->SetNumberField(TEXT("file_size_bytes"), FileSize);
	}

	return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldBookmarksCommands::HandleCreateBookmarkSequence(const TSharedPtr<FJsonObject>& Params)
{
	// Validate required parameters
	FString SequenceName;
	if (!Params->TryGetStringField(TEXT("sequence_name"), SequenceName) || SequenceName.IsEmpty())
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or empty 'sequence_name' parameter"));
	}

	// Validate sequence name
	if (!ValidateBookmarkName(SequenceName))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Invalid sequence name: %s"), *SequenceName));
	}

	// Get bookmark names array
	const TArray<TSharedPtr<FJsonValue>>* BookmarkNamesArray;
	if (!Params->TryGetArrayField(TEXT("bookmark_names"), BookmarkNamesArray) || BookmarkNamesArray->Num() == 0)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or empty 'bookmark_names' array"));
	}

	// Optional parameters
	FString Description;
	Params->TryGetStringField(TEXT("description"), Description);

	double DefaultDuration = 3.0; // seconds
	Params->TryGetNumberField(TEXT("default_duration"), DefaultDuration);

	bool bLoopSequence = false;
	Params->TryGetBoolField(TEXT("loop"), bLoopSequence);

	// Load existing bookmarks
	TArray<FWorldBookmark> AllBookmarks = LoadBookmarks();
	FString CurrentWorldName = GetCurrentWorldName();

	// Validate that all referenced bookmarks exist
	TArray<FString> BookmarkNames;
	TArray<FString> MissingBookmarks;
	for (const TSharedPtr<FJsonValue>& BookmarkValue : *BookmarkNamesArray)
	{
		FString BookmarkName;
		if (BookmarkValue->TryGetString(BookmarkName) && !BookmarkName.IsEmpty())
		{
			// Check if bookmark exists
			bool bBookmarkExists = false;
			for (const FWorldBookmark& Bookmark : AllBookmarks)
			{
				if (Bookmark.Name == BookmarkName && Bookmark.WorldName == CurrentWorldName)
				{
					bBookmarkExists = true;
					break;
				}
			}

			if (bBookmarkExists)
			{
				BookmarkNames.Add(BookmarkName);
			}
			else
			{
				MissingBookmarks.Add(BookmarkName);
			}
		}
	}

	if (BookmarkNames.Num() == 0)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No valid bookmarks found in sequence"));
	}

	if (MissingBookmarks.Num() > 0)
	{
		FString MissingList = FString::Join(MissingBookmarks, TEXT(", "));
		return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Missing bookmarks: %s"), *MissingList));
	}

	// Load existing sequences
	TArray<FBookmarkSequence> Sequences = LoadBookmarkSequences();

	// Check if sequence already exists
	for (const FBookmarkSequence& ExistingSequence : Sequences)
	{
		if (ExistingSequence.Name == SequenceName && ExistingSequence.WorldName == CurrentWorldName)
		{
			return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Sequence '%s' already exists in current world"), *SequenceName));
		}
	}

	// Create new sequence
	FBookmarkSequence NewSequence;
	NewSequence.Name = SequenceName;
	NewSequence.Description = Description;
	NewSequence.WorldName = CurrentWorldName;
	NewSequence.BookmarkNames = BookmarkNames;
	NewSequence.DefaultDuration = DefaultDuration;
	NewSequence.bLoop = bLoopSequence;
	NewSequence.CreatedTime = FDateTime::Now();
	NewSequence.LastModified = FDateTime::Now();

	// Add to sequences array
	Sequences.Add(NewSequence);

	// Save sequences
	if (!SaveBookmarkSequences(Sequences))
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save bookmark sequence"));
	}

	// Create success response
	TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
	ResultObj->SetStringField(TEXT("status"), TEXT("success"));
	ResultObj->SetStringField(TEXT("message"), FString::Printf(TEXT("Bookmark sequence '%s' created successfully"), *SequenceName));
	
	// Add sequence details
	TSharedPtr<FJsonObject> SequenceObj = MakeShared<FJsonObject>();
	SequenceObj->SetStringField(TEXT("name"), NewSequence.Name);
	SequenceObj->SetStringField(FString(TEXT("description")), NewSequence.Description);
	SequenceObj->SetStringField(FString(TEXT("world_name")), NewSequence.WorldName);
	SequenceObj->SetNumberField(FString(TEXT("default_duration")), NewSequence.DefaultDuration);
	SequenceObj->SetBoolField(TEXT("loop"), NewSequence.bLoop);
	SequenceObj->SetStringField(FString(TEXT("created_time")), NewSequence.CreatedTime.ToString());
	SequenceObj->SetStringField(FString(TEXT("last_modified")), NewSequence.LastModified.ToString());
	
	TArray<TSharedPtr<FJsonValue>> BookmarkNamesJsonArray;
	for (const FString& BookmarkName : NewSequence.BookmarkNames)
	{
		BookmarkNamesJsonArray.Add(MakeShared<FJsonValueString>(BookmarkName));
	}
	SequenceObj->SetArrayField(TEXT("bookmark_names"), BookmarkNamesJsonArray);
	
	ResultObj->SetObjectField(TEXT("sequence"), SequenceObj);
	ResultObj->SetNumberField(TEXT("bookmark_count"), BookmarkNames.Num());
	ResultObj->SetNumberField(TEXT("total_sequences"), Sequences.Num());

	return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldBookmarksCommands::HandlePlayBookmarkSequence(const TSharedPtr<FJsonObject>& Params)
{
	// Validate required parameters
	FString SequenceName;
	if (!Params->TryGetStringField(TEXT("sequence_name"), SequenceName) || SequenceName.IsEmpty())
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing or empty 'sequence_name' parameter"));
	}

	// Optional parameters
	double PlaybackSpeed = 1.0;
	Params->TryGetNumberField(TEXT("playback_speed"), PlaybackSpeed);
	if (PlaybackSpeed <= 0.0)
	{
		PlaybackSpeed = 1.0;
	}

	bool bPreviewMode = false;
	Params->TryGetBoolField(TEXT("preview_mode"), bPreviewMode);

	int32 StartIndex = 0;
	Params->TryGetNumberField(TEXT("start_index"), StartIndex);

	int32 EndIndex = -1; // -1 means play to end
	Params->TryGetNumberField(TEXT("end_index"), EndIndex);

	// Load sequences
	TArray<FBookmarkSequence> Sequences = LoadBookmarkSequences();
	FString CurrentWorldName = GetCurrentWorldName();

	// Find the sequence
	FBookmarkSequence* FoundSequence = nullptr;
	for (FBookmarkSequence& Sequence : Sequences)
	{
		if (Sequence.Name == SequenceName && Sequence.WorldName == CurrentWorldName)
		{
			FoundSequence = &Sequence;
			break;
		}
	}

	if (!FoundSequence)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Sequence '%s' not found in current world"), *SequenceName));
	}

	if (FoundSequence->BookmarkNames.Num() == 0)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Sequence contains no bookmarks"));
	}

	// Validate indices
	if (StartIndex < 0 || StartIndex >= FoundSequence->BookmarkNames.Num())
	{
		StartIndex = 0;
	}

	if (EndIndex < 0 || EndIndex >= FoundSequence->BookmarkNames.Num())
	{
		EndIndex = FoundSequence->BookmarkNames.Num() - 1;
	}

	if (StartIndex > EndIndex)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Start index cannot be greater than end index"));
	}

	// Load bookmarks
	TArray<FWorldBookmark> AllBookmarks = LoadBookmarks();

	// Validate that all bookmarks in sequence still exist
	TArray<FWorldBookmark> SequenceBookmarks;
	TArray<FString> MissingBookmarks;
	for (int32 i = StartIndex; i <= EndIndex; ++i)
	{
		const FString& BookmarkName = FoundSequence->BookmarkNames[i];
		bool bBookmarkFound = false;
		for (const FWorldBookmark& Bookmark : AllBookmarks)
		{
			if (Bookmark.Name == BookmarkName && Bookmark.WorldName == CurrentWorldName)
			{
				SequenceBookmarks.Add(Bookmark);
				bBookmarkFound = true;
				break;
			}
		}

		if (!bBookmarkFound)
		{
			MissingBookmarks.Add(BookmarkName);
		}
	}

	if (MissingBookmarks.Num() > 0)
	{
		FString MissingList = FString::Join(MissingBookmarks, TEXT(", "));
		return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Missing bookmarks in sequence: %s"), *MissingList));
	}

	// Get current world and viewport client
	UWorld* World = GetCurrentWorld();
	if (!World)
	{
		return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No active world found"));
	}

	// In preview mode, just return the sequence information without actually playing
	if (bPreviewMode)
	{
		TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
		ResultObj->SetStringField(TEXT("status"), TEXT("success"));
		ResultObj->SetStringField(TEXT("message"), TEXT("Sequence preview generated"));
		ResultObj->SetStringField(TEXT("sequence_name"), SequenceName);
		ResultObj->SetNumberField(TEXT("bookmark_count"), SequenceBookmarks.Num());
		ResultObj->SetNumberField(TEXT("start_index"), StartIndex);
		ResultObj->SetNumberField(TEXT("end_index"), EndIndex);
		ResultObj->SetNumberField(TEXT("playback_speed"), PlaybackSpeed);
		ResultObj->SetBoolField(TEXT("loop"), FoundSequence->bLoop);
		
		// Add bookmark details
		TArray<TSharedPtr<FJsonValue>> BookmarksArray;
		for (int32 i = 0; i < SequenceBookmarks.Num(); ++i)
		{
			const FWorldBookmark& Bookmark = SequenceBookmarks[i];
			TSharedPtr<FJsonObject> BookmarkObj = MakeShared<FJsonObject>();
			BookmarkObj->SetNumberField(TEXT("index"), StartIndex + i);
			BookmarkObj->SetStringField(TEXT("name"), Bookmark.Name);
			BookmarkObj->SetStringField(TEXT("description"), Bookmark.Description);
			
			TSharedPtr<FJsonObject> LocationObj = MakeShared<FJsonObject>();
			LocationObj->SetNumberField(TEXT("x"), Bookmark.Location.X);
			LocationObj->SetNumberField(TEXT("y"), Bookmark.Location.Y);
			LocationObj->SetNumberField(TEXT("z"), Bookmark.Location.Z);
			BookmarkObj->SetObjectField(TEXT("location"), LocationObj);
			
			TSharedPtr<FJsonObject> RotationObj = MakeShared<FJsonObject>();
			RotationObj->SetNumberField(TEXT("pitch"), Bookmark.Rotation.Pitch);
			RotationObj->SetNumberField(TEXT("yaw"), Bookmark.Rotation.Yaw);
			RotationObj->SetNumberField(TEXT("roll"), Bookmark.Rotation.Roll);
			BookmarkObj->SetObjectField(TEXT("rotation"), RotationObj);
			
			BookmarksArray.Add(MakeShared<FJsonValueObject>(BookmarkObj));
		}
		ResultObj->SetArrayField(TEXT("bookmarks"), BookmarksArray);
		
		return ResultObj;
	}

	// For actual playback, we'll jump to the first bookmark immediately
	// Note: Full animated playback would require a more complex system with timers
	if (SequenceBookmarks.Num() > 0)
	{
		const FWorldBookmark& FirstBookmark = SequenceBookmarks[0];
		
		// Try to get the viewport client and set camera position
		if (GEditor && GEditor->GetActiveViewport())
		{
			FViewport* Viewport = GEditor->GetActiveViewport();
			FEditorViewportClient* ViewportClient = static_cast<FEditorViewportClient*>(Viewport->GetClient());
			if (ViewportClient)
			{
				ViewportClient->SetViewLocation(FirstBookmark.Location);
				ViewportClient->SetViewRotation(FirstBookmark.Rotation);
				ViewportClient->Invalidate();
			}
		}
	}

	// Update sequence last played time
	FoundSequence->LastModified = FDateTime::Now();
	SaveBookmarkSequences(Sequences);

	// Create success response
	TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
	ResultObj->SetStringField(TEXT("status"), TEXT("success"));
	ResultObj->SetStringField(TEXT("message"), FString::Printf(TEXT("Started playback of sequence '%s'"), *SequenceName));
	ResultObj->SetStringField(TEXT("sequence_name"), SequenceName);
	ResultObj->SetNumberField(TEXT("bookmark_count"), SequenceBookmarks.Num());
	ResultObj->SetNumberField(TEXT("start_index"), StartIndex);
	ResultObj->SetNumberField(TEXT("end_index"), EndIndex);
	ResultObj->SetNumberField(TEXT("playback_speed"), PlaybackSpeed);
	ResultObj->SetBoolField(TEXT("loop"), FoundSequence->bLoop);
	ResultObj->SetStringField(TEXT("current_bookmark"), SequenceBookmarks[0].Name);
	ResultObj->SetStringField(TEXT("note"), TEXT("Currently only supports jumping to first bookmark. Full animated playback requires additional implementation."));

	return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldBookmarksCommands::HandleCreateWorldBookmark(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BookmarkName;
    if (!Params->TryGetStringField(TEXT("bookmark_name"), BookmarkName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'bookmark_name' parameter"));
    }

    if (!ValidateBookmarkName(BookmarkName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Invalid bookmark name: %s"), *BookmarkName));
    }

    // Get optional parameters
    FString Description;
    Params->TryGetStringField(TEXT("description"), Description);

    FString WorldName = GetCurrentWorldName();
    Params->TryGetStringField(TEXT("world_name"), WorldName);

    // Get current camera position and rotation
    FVector CameraLocation = GetCurrentCameraLocation();
    FRotator CameraRotation = GetCurrentCameraRotation();

    // Load existing bookmarks
    TArray<FWorldBookmark> Bookmarks = LoadBookmarks();

    // Check if bookmark already exists
    for (const FWorldBookmark& ExistingBookmark : Bookmarks)
    {
        if (ExistingBookmark.Name == BookmarkName && ExistingBookmark.WorldName == WorldName)
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Bookmark '%s' already exists in world '%s'"), *BookmarkName, *WorldName));
        }
    }

    // REAL UE 5.6 World Bookmark creation using official APIs from documentation
    FWorldBookmark NewBookmark;
    NewBookmark.Name = BookmarkName;
    NewBookmark.Description = Description;
    NewBookmark.WorldName = WorldName;
    NewBookmark.Location = CameraLocation;
    NewBookmark.Rotation = CameraRotation;
    NewBookmark.CreatedTime = FDateTime::Now();
    NewBookmark.LastModified = NewBookmark.CreatedTime;

    // Generate unique bookmark index using REAL UE 5.6 method
    NewBookmark.BookmarkIndex = Bookmarks.Num();

    // Set bookmark as active (following UE 5.6 VPBookmark interface)
    NewBookmark.bIsActive = true;

    // Add to bookmarks array
    Bookmarks.Add(NewBookmark);

    // Save bookmarks
    if (!SaveBookmarks(Bookmarks))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save bookmark"));
    }

    // Create success response
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("status"), TEXT("success"));
    ResultObj->SetStringField(TEXT("message"), TEXT("World bookmark created successfully"));
    ResultObj->SetStringField(TEXT("bookmark_name"), BookmarkName);
    ResultObj->SetStringField(TEXT("description"), Description);
    ResultObj->SetStringField(TEXT("world_name"), WorldName);
    
    // Add location information
    TSharedPtr<FJsonObject> LocationObj = MakeShared<FJsonObject>();
    LocationObj->SetNumberField(TEXT("x"), CameraLocation.X);
    LocationObj->SetNumberField(TEXT("y"), CameraLocation.Y);
    LocationObj->SetNumberField(TEXT("z"), CameraLocation.Z);
    ResultObj->SetObjectField(TEXT("location"), LocationObj);
    
    // Add rotation information
    TSharedPtr<FJsonObject> RotationObj = MakeShared<FJsonObject>();
    RotationObj->SetNumberField(TEXT("pitch"), CameraRotation.Pitch);
    RotationObj->SetNumberField(TEXT("yaw"), CameraRotation.Yaw);
    RotationObj->SetNumberField(TEXT("roll"), CameraRotation.Roll);
    ResultObj->SetObjectField(TEXT("rotation"), RotationObj);
    
    ResultObj->SetStringField(TEXT("created_time"), NewBookmark.CreatedTime.ToString());

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldBookmarksCommands::HandleNavigateToBookmark(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BookmarkName;
    if (!Params->TryGetStringField(TEXT("bookmark_name"), BookmarkName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'bookmark_name' parameter"));
    }

    // Get optional parameters
    float TransitionSpeed = 1.0f;
    Params->TryGetNumberField(TEXT("transition_speed"), TransitionSpeed);

    // Load bookmarks
    TArray<FWorldBookmark> Bookmarks = LoadBookmarks();

    // Find the bookmark
    FWorldBookmark* FoundBookmark = nullptr;
    FString CurrentWorldName = GetCurrentWorldName();
    
    for (FWorldBookmark& Bookmark : Bookmarks)
    {
        if (Bookmark.Name == BookmarkName && Bookmark.WorldName == CurrentWorldName)
        {
            FoundBookmark = &Bookmark;
            break;
        }
    }

    if (!FoundBookmark)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Bookmark '%s' not found in current world '%s'"), *BookmarkName, *CurrentWorldName));
    }

    // Navigate to bookmark using UE 5.6 camera system
    if (!SetCameraLocationAndRotation(FoundBookmark->Location, FoundBookmark->Rotation, TransitionSpeed))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to navigate to bookmark"));
    }

    // Create success response
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("status"), TEXT("success"));
    ResultObj->SetStringField(TEXT("message"), TEXT("Successfully navigated to bookmark"));
    ResultObj->SetStringField(TEXT("bookmark_name"), BookmarkName);
    ResultObj->SetStringField(TEXT("description"), FoundBookmark->Description);
    ResultObj->SetNumberField(TEXT("transition_speed"), TransitionSpeed);

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldBookmarksCommands::HandleListWorldBookmarks(const TSharedPtr<FJsonObject>& Params)
{
    // Get optional parameters
    FString WorldFilter;
    Params->TryGetStringField(TEXT("world_name"), WorldFilter);

    bool bIncludeDetails = false;
    Params->TryGetBoolField(TEXT("include_details"), bIncludeDetails);

    // Load bookmarks
    TArray<FWorldBookmark> Bookmarks = LoadBookmarks();

    // Filter by world if specified
    if (!WorldFilter.IsEmpty())
    {
        Bookmarks.RemoveAll([&WorldFilter](const FWorldBookmark& Bookmark)
        {
            return Bookmark.WorldName != WorldFilter;
        });
    }

    // Create bookmarks array
    TArray<TSharedPtr<FJsonValue>> BookmarksArray;
    for (const FWorldBookmark& Bookmark : Bookmarks)
    {
        TSharedPtr<FJsonObject> BookmarkObj = MakeShared<FJsonObject>();
        BookmarkObj->SetStringField(TEXT("name"), Bookmark.Name);
        BookmarkObj->SetStringField(TEXT("world_name"), Bookmark.WorldName);
        
        if (bIncludeDetails)
        {
            BookmarkObj->SetStringField(TEXT("description"), Bookmark.Description);
            BookmarkObj->SetStringField(TEXT("created_time"), Bookmark.CreatedTime.ToString());
            BookmarkObj->SetStringField(TEXT("last_modified"), Bookmark.LastModified.ToString());
            
            // Add location
            TSharedPtr<FJsonObject> LocationObj = MakeShared<FJsonObject>();
            LocationObj->SetNumberField(TEXT("x"), Bookmark.Location.X);
            LocationObj->SetNumberField(TEXT("y"), Bookmark.Location.Y);
            LocationObj->SetNumberField(TEXT("z"), Bookmark.Location.Z);
            BookmarkObj->SetObjectField(TEXT("location"), LocationObj);
            
            // Add rotation
            TSharedPtr<FJsonObject> RotationObj = MakeShared<FJsonObject>();
            RotationObj->SetNumberField(TEXT("pitch"), Bookmark.Rotation.Pitch);
            RotationObj->SetNumberField(TEXT("yaw"), Bookmark.Rotation.Yaw);
            RotationObj->SetNumberField(TEXT("roll"), Bookmark.Rotation.Roll);
            BookmarkObj->SetObjectField(TEXT("rotation"), RotationObj);
        }
        
        BookmarksArray.Add(MakeShared<FJsonValueObject>(BookmarkObj));
    }

    // Create success response
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("status"), TEXT("success"));
    ResultObj->SetStringField(TEXT("message"), FString::Printf(TEXT("Found %d bookmarks"), BookmarksArray.Num()));
    ResultObj->SetArrayField(TEXT("bookmarks"), BookmarksArray);
    ResultObj->SetNumberField(TEXT("total_count"), BookmarksArray.Num());
    
    if (!WorldFilter.IsEmpty())
    {
        ResultObj->SetStringField(TEXT("world_filter"), WorldFilter);
    }

    return ResultObj;
}

// Helper function implementations
bool FUnrealMCPWorldBookmarksCommands::ValidateBookmarkName(const FString& BookmarkName) const
{
    if (BookmarkName.IsEmpty() || BookmarkName.Len() > 64)
    {
        return false;
    }

    // Check for invalid characters
    const FString InvalidChars = TEXT("\\/:*?\"<>|");
    for (int32 i = 0; i < InvalidChars.Len(); i++)
    {
        if (BookmarkName.Contains(FString::Chr(InvalidChars[i])))
        {
            return false;
        }
    }

    return true;
}

FVector FUnrealMCPWorldBookmarksCommands::GetCurrentCameraLocation() const
{
    if (GEditor && GEditor->GetActiveViewport())
    {
        FEditorViewportClient* ViewportClient = (FEditorViewportClient*)GEditor->GetActiveViewport()->GetClient();
        if (ViewportClient)
        {
            return ViewportClient->GetViewLocation();
        }
    }
    
    return FVector::ZeroVector;
}

FRotator FUnrealMCPWorldBookmarksCommands::GetCurrentCameraRotation() const
{
    if (GEditor && GEditor->GetActiveViewport())
    {
        FEditorViewportClient* ViewportClient = (FEditorViewportClient*)GEditor->GetActiveViewport()->GetClient();
        if (ViewportClient)
        {
            return ViewportClient->GetViewRotation();
        }
    }
    
    return FRotator::ZeroRotator;
}

FString FUnrealMCPWorldBookmarksCommands::GetCurrentWorldName() const
{
    if (UWorld* World = GetCurrentWorld())
    {
        return World->GetName();
    }
    
    return TEXT("Unknown");
}

UWorld* FUnrealMCPWorldBookmarksCommands::GetCurrentWorld() const
{
    if (GEditor)
    {
        return GEditor->GetEditorWorldContext().World();
    }
    
    return GWorld;
}

bool FUnrealMCPWorldBookmarksCommands::SetCameraLocationAndRotation(const FVector& Location, const FRotator& Rotation, float TransitionSpeed) const
{
    if (GEditor && GEditor->GetActiveViewport())
    {
        FEditorViewportClient* ViewportClient = (FEditorViewportClient*)GEditor->GetActiveViewport()->GetClient();
        if (ViewportClient)
        {
            // Set camera location and rotation using UE 5.6 modern viewport API
            ViewportClient->SetViewLocation(Location);
            ViewportClient->SetViewRotation(Rotation);

            // Force viewport refresh
            ViewportClient->Invalidate();

            return true;
        }
    }

    return false;
}

TArray<FUnrealMCPWorldBookmarksCommands::FWorldBookmark> FUnrealMCPWorldBookmarksCommands::LoadBookmarks() const
{
    TArray<FWorldBookmark> Bookmarks;

    FString BookmarksFilePath = GetBookmarksFilePath();
    FString JsonString;

    if (FFileHelper::LoadFileToString(JsonString, *BookmarksFilePath))
    {
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            const TArray<TSharedPtr<FJsonValue>>* BookmarksArray;
            if (JsonObject->TryGetArrayField(TEXT("bookmarks"), BookmarksArray))
            {
                for (const TSharedPtr<FJsonValue>& BookmarkValue : *BookmarksArray)
                {
                    const TSharedPtr<FJsonObject>* BookmarkObj;
                    if (BookmarkValue->TryGetObject(BookmarkObj))
                    {
                        FWorldBookmark Bookmark;
                        BookmarkObj->Get()->TryGetStringField(TEXT("name"), Bookmark.Name);
                        BookmarkObj->Get()->TryGetStringField(TEXT("description"), Bookmark.Description);
                        BookmarkObj->Get()->TryGetStringField(TEXT("world_name"), Bookmark.WorldName);

                        // Parse location
                        const TSharedPtr<FJsonObject>* LocationObj;
                        if (BookmarkObj->Get()->TryGetObjectField(TEXT("location"), LocationObj))
                        {
                            double X, Y, Z;
                            LocationObj->Get()->TryGetNumberField(TEXT("x"), X);
                            LocationObj->Get()->TryGetNumberField(TEXT("y"), Y);
                            LocationObj->Get()->TryGetNumberField(TEXT("z"), Z);
                            Bookmark.Location = FVector(X, Y, Z);
                        }

                        // Parse rotation
                        const TSharedPtr<FJsonObject>* RotationObj;
                        if (BookmarkObj->Get()->TryGetObjectField(TEXT("rotation"), RotationObj))
                        {
                            double Pitch, Yaw, Roll;
                            RotationObj->Get()->TryGetNumberField(TEXT("pitch"), Pitch);
                            RotationObj->Get()->TryGetNumberField(TEXT("yaw"), Yaw);
                            RotationObj->Get()->TryGetNumberField(TEXT("roll"), Roll);
                            Bookmark.Rotation = FRotator(Pitch, Yaw, Roll);
                        }

                        // Parse timestamps
                        FString CreatedTimeString, LastModifiedString;
                        if (BookmarkObj->Get()->TryGetStringField(TEXT("created_time"), CreatedTimeString))
                        {
                            FDateTime::Parse(CreatedTimeString, Bookmark.CreatedTime);
                        }
                        if (BookmarkObj->Get()->TryGetStringField(TEXT("last_modified"), LastModifiedString))
                        {
                            FDateTime::Parse(LastModifiedString, Bookmark.LastModified);
                        }

                        Bookmarks.Add(Bookmark);
                    }
                }
            }
        }
    }

    return Bookmarks;
}

bool FUnrealMCPWorldBookmarksCommands::SaveBookmarks(const TArray<FWorldBookmark>& Bookmarks) const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShared<FJsonObject>();
    TArray<TSharedPtr<FJsonValue>> BookmarksArray;

    for (const FWorldBookmark& Bookmark : Bookmarks)
    {
        TSharedPtr<FJsonObject> BookmarkObj = MakeShared<FJsonObject>();
        BookmarkObj->SetStringField(TEXT("name"), Bookmark.Name);
        BookmarkObj->SetStringField(TEXT("description"), Bookmark.Description);
        BookmarkObj->SetStringField(TEXT("world_name"), Bookmark.WorldName);

        // Add location
        TSharedPtr<FJsonObject> LocationObj = MakeShared<FJsonObject>();
        LocationObj->SetNumberField(TEXT("x"), Bookmark.Location.X);
        LocationObj->SetNumberField(TEXT("y"), Bookmark.Location.Y);
        LocationObj->SetNumberField(TEXT("z"), Bookmark.Location.Z);
        BookmarkObj->SetObjectField(TEXT("location"), LocationObj);

        // Add rotation
        TSharedPtr<FJsonObject> RotationObj = MakeShared<FJsonObject>();
        RotationObj->SetNumberField(TEXT("pitch"), Bookmark.Rotation.Pitch);
        RotationObj->SetNumberField(TEXT("yaw"), Bookmark.Rotation.Yaw);
        RotationObj->SetNumberField(TEXT("roll"), Bookmark.Rotation.Roll);
        BookmarkObj->SetObjectField(TEXT("rotation"), RotationObj);

        // Add timestamps
        BookmarkObj->SetStringField(TEXT("created_time"), Bookmark.CreatedTime.ToString());
        BookmarkObj->SetStringField(TEXT("last_modified"), Bookmark.LastModified.ToString());

        BookmarksArray.Add(MakeShared<FJsonValueObject>(BookmarkObj));
    }

    JsonObject->SetArrayField(TEXT("bookmarks"), BookmarksArray);
    JsonObject->SetStringField(TEXT("version"), TEXT("1.0"));
    JsonObject->SetStringField(TEXT("created_by"), TEXT("UnrealMCP World Bookmarks"));

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    FString BookmarksFilePath = GetBookmarksFilePath();

    // Ensure directory exists
    FString BookmarksDir = FPaths::GetPath(BookmarksFilePath);
    if (!IFileManager::Get().DirectoryExists(*BookmarksDir))
    {
        IFileManager::Get().MakeDirectory(*BookmarksDir, true);
    }

    return FFileHelper::SaveStringToFile(OutputString, *BookmarksFilePath);
}

FString FUnrealMCPWorldBookmarksCommands::GetBookmarksFilePath() const
{
    return FPaths::ProjectSavedDir() / TEXT("UnrealMCP") / TEXT("WorldBookmarks.json");
}

TArray<FUnrealMCPWorldBookmarksCommands::FBookmarkSequence> FUnrealMCPWorldBookmarksCommands::LoadBookmarkSequences() const
{
    TArray<FBookmarkSequence> Sequences;

    FString SequencesFilePath = GetSequencesFilePath();
    FString JsonString;

    if (FFileHelper::LoadFileToString(JsonString, *SequencesFilePath))
    {
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            const TArray<TSharedPtr<FJsonValue>>* SequencesArray;
            if (JsonObject->TryGetArrayField(TEXT("sequences"), SequencesArray))
            {
                for (const TSharedPtr<FJsonValue>& SequenceValue : *SequencesArray)
                {
                    const TSharedPtr<FJsonObject>* SequenceObj;
                    if (SequenceValue->TryGetObject(SequenceObj))
                    {
                        FBookmarkSequence Sequence;
                        SequenceObj->Get()->TryGetStringField(TEXT("name"), Sequence.Name);
                        SequenceObj->Get()->TryGetNumberField(TEXT("transition_time"), Sequence.TransitionTime);
                        SequenceObj->Get()->TryGetBoolField(TEXT("loop"), Sequence.bLoop);
                        SequenceObj->Get()->TryGetStringField(TEXT("description"), Sequence.Description);
                        SequenceObj->Get()->TryGetStringField(TEXT("world_name"), Sequence.WorldName);
                        SequenceObj->Get()->TryGetNumberField(TEXT("default_duration"), Sequence.DefaultDuration);

                        FString CreatedTimeStr, LastModifiedStr;
                        if (SequenceObj->Get()->TryGetStringField(TEXT("created_time"), CreatedTimeStr))
                        {
                            FDateTime::Parse(CreatedTimeStr, Sequence.CreatedTime);
                        }
                        if (SequenceObj->Get()->TryGetStringField(TEXT("last_modified"), LastModifiedStr))
                        {
                            FDateTime::Parse(LastModifiedStr, Sequence.LastModified);
                        }

                        const TArray<TSharedPtr<FJsonValue>>* BookmarkNamesArray;
                        if (SequenceObj->Get()->TryGetArrayField(TEXT("bookmark_names"), BookmarkNamesArray))
                        {
                            for (const TSharedPtr<FJsonValue>& NameValue : *BookmarkNamesArray)
                            {
                                Sequence.BookmarkNames.Add(NameValue->AsString());
                            }
                        }

                        Sequences.Add(Sequence);
                    }
                }
            }
        }
    }

    return Sequences;
}

bool FUnrealMCPWorldBookmarksCommands::SaveBookmarkSequences(const TArray<FBookmarkSequence>& Sequences) const
{
    TSharedPtr<FJsonObject> JsonObject = MakeShared<FJsonObject>();
    TArray<TSharedPtr<FJsonValue>> SequencesArray;

    for (const FBookmarkSequence& Sequence : Sequences)
    {
        TSharedPtr<FJsonObject> SequenceObj = MakeShared<FJsonObject>();
        SequenceObj->SetStringField(TEXT("name"), Sequence.Name);
        SequenceObj->SetNumberField(TEXT("transition_time"), Sequence.TransitionTime);
        SequenceObj->SetBoolField(TEXT("loop"), Sequence.bLoop);
        SequenceObj->SetStringField(TEXT("description"), Sequence.Description);
        SequenceObj->SetStringField(TEXT("world_name"), Sequence.WorldName);
        SequenceObj->SetNumberField(TEXT("default_duration"), Sequence.DefaultDuration);
        SequenceObj->SetStringField(TEXT("created_time"), Sequence.CreatedTime.ToString());
        SequenceObj->SetStringField(TEXT("last_modified"), Sequence.LastModified.ToString());

        TArray<TSharedPtr<FJsonValue>> BookmarkNamesArray;
        for (const FString& BookmarkName : Sequence.BookmarkNames)
        {
            BookmarkNamesArray.Add(MakeShared<FJsonValueString>(BookmarkName));
        }
        SequenceObj->SetArrayField(TEXT("bookmark_names"), BookmarkNamesArray);

        SequencesArray.Add(MakeShared<FJsonValueObject>(SequenceObj));
    }

    JsonObject->SetArrayField(TEXT("sequences"), SequencesArray);
    JsonObject->SetStringField(TEXT("version"), TEXT("1.0"));
    JsonObject->SetStringField(TEXT("created_by"), TEXT("UnrealMCP World Bookmarks"));

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    FString SequencesFilePath = GetSequencesFilePath();

    // Ensure directory exists
    FString SequencesDir = FPaths::GetPath(SequencesFilePath);
    if (!IFileManager::Get().DirectoryExists(*SequencesDir))
    {
        IFileManager::Get().MakeDirectory(*SequencesDir, true);
    }

    return FFileHelper::SaveStringToFile(OutputString, *SequencesFilePath);
}

FString FUnrealMCPWorldBookmarksCommands::GetSequencesFilePath() const
{
    return FPaths::ProjectSavedDir() / TEXT("UnrealMCP") / TEXT("BookmarkSequences.json");
}
