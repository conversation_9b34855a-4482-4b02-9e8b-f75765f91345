# 📋 DOCUMENTAÇÃO OFICIAL - Headers e APIs UE 5.6 para Data Layers e World Partition

**Data:** 30 de Agosto de 2025  
**Versão:** Unreal Engine 5.6  
**Status:** ✅ **VERIFICADO COM DOCUMENTAÇÃO OFICIAL**  

---

## 🎯 HEADERS CORRETOS PARA DATA LAYERS (UE 5.6)

### Core Data Layer Headers
```cpp
// Data Layer Assets e Instances
#include "WorldPartition/DataLayer/DataLayerAsset.h"           // UDataLayerAsset
#include "WorldPartition/DataLayer/DataLayerInstance.h"        // UDataLayerInstance
#include "WorldPartition/DataLayer/DataLayerInstanceWithAsset.h" // UDataLayerInstanceWithAsset
#include "WorldPartition/DataLayer/DataLayerInstancePrivate.h" // UDataLayerInstancePrivate

// Data Layer Manager e Subsystem
#include "WorldPartition/DataLayer/DataLayerManager.h"         // UDataLayerManager
#include "WorldPartition/DataLayer/WorldDataLayers.h"         // AWorldDataLayers

// Data Layer Utils e Helpers
#include "WorldPartition/DataLayer/DataLayerUtils.h"          // FDataLayerUtils
#include "WorldPartition/DataLayer/ActorDataLayer.h"          // FActorDataLayer
```

### External Data Layers (UE 5.6 Feature)
```cpp
#include "WorldPartition/DataLayer/ExternalDataLayerAsset.h"   // UExternalDataLayerAsset
#include "WorldPartition/DataLayer/ExternalDataLayerInstance.h" // UExternalDataLayerInstance
#include "WorldPartition/DataLayer/ExternalDataLayerManager.h" // UExternalDataLayerManager
```

### Data Layer Editor Support
```cpp
#include "WorldPartition/DataLayer/DataLayerEditorContext.h"   // FDataLayerEditorContext
#include "WorldPartition/DataLayer/DataLayersEditorBroadcast.h" // FDataLayersEditorBroadcast
```

---

## 🌍 HEADERS CORRETOS PARA WORLD PARTITION (UE 5.6)

### Core World Partition Headers
```cpp
// World Partition Core
#include "WorldPartition/WorldPartition.h"                    // UWorldPartition
#include "WorldPartition/WorldPartitionSubsystem.h"           // UWorldPartitionSubsystem
#include "WorldPartition/WorldPartitionStreamingPolicy.h"     // UWorldPartitionStreamingPolicy

// World Partition Cells
#include "WorldPartition/WorldPartitionRuntimeCell.h"         // UWorldPartitionRuntimeCell
#include "WorldPartition/IWorldPartitionCell.h"               // IWorldPartitionCell
```

### HLOD (Hierarchical Level of Detail)
```cpp
#include "WorldPartition/HLOD/HLODSubsystem.h"               // UHLODSubsystem
#include "WorldPartition/HLOD/HLODBuilder.h"                 // UHLODBuilder
#include "WorldPartition/HLOD/HLODLayer.h"                   // UHLODLayer
```

### World Partition Streaming
```cpp
#include "WorldPartition/WorldPartitionStreamingSource.h"     // UWorldPartitionStreamingSource
#include "WorldPartition/WorldPartitionLevelStreamingDynamic.h" // UWorldPartitionLevelStreamingDynamic
```

---

## 📊 ENUMS E TIPOS IMPORTANTES (UE 5.6)

### Data Layer Enums
```cpp
// Estado de Runtime dos Data Layers
enum class EDataLayerRuntimeState : uint8
{
    Unloaded,    // Não carregado
    Loaded,      // Carregado mas não visível
    Activated    // Carregado e visível
};

// Tipo de Data Layer
enum class EDataLayerType : uint8
{
    Runtime,     // Pode ser controlado em runtime
    Editor       // Apenas para organização no editor
};

// Filtro de carregamento
enum class EDataLayerLoadFilter : uint8
{
    All,
    Game,
    Editor
};
```

### World Partition Enums
```cpp
// Estado de streaming
enum class EWorldPartitionStreamingPerformance : uint8
{
    Good,
    Slow,
    Critical
};
```

---

## 🔧 CLASSES PRINCIPAIS E SUAS FUNÇÕES (UE 5.6)

### UDataLayerAsset
```cpp
class UNREALMCP_API UDataLayerAsset : public UObject
{
    // Propriedades principais
    UPROPERTY() FString DataLayerLabel;
    UPROPERTY() EDataLayerType DataLayerType;
    UPROPERTY() FColor DebugColor;
    
    // Funções principais
    const FString& GetDataLayerLabel() const;
    EDataLayerType GetDataLayerType() const;
    FColor GetDebugColor() const;
};
```

### UDataLayerInstance
```cpp
class UNREALMCP_API UDataLayerInstance : public UObject
{
    // Funções de estado
    EDataLayerRuntimeState GetRuntimeState() const;
    bool IsVisible() const;
    bool IsLoaded() const;
    bool IsRuntime() const;
    
    // Funções de controle
    void SetIsVisible(bool bIsVisible);
    void SetIsLoaded(bool bIsLoaded);
};
```

### UDataLayerManager
```cpp
class UNREALMCP_API UDataLayerManager : public UObject
{
    // Funções de busca
    const UDataLayerInstance* GetDataLayerInstance(const UDataLayerAsset* DataLayerAsset) const;
    const UDataLayerInstance* GetDataLayerInstanceFromAssetName(const FString& InDataLayerAssetName) const;
    
    // Funções de controle
    void SetDataLayerInstanceRuntimeState(const UDataLayerInstance* DataLayerInstance, EDataLayerRuntimeState State);
    void SetDataLayerRuntimeState(const UDataLayerAsset* DataLayerAsset, EDataLayerRuntimeState State);
    
    // Funções de query
    TArray<const UDataLayerInstance*> GetDataLayerInstances(const TArray<const UDataLayerAsset*>& DataLayerAssets) const;
};
```

---

## 🛠️ MÓDULOS NECESSÁRIOS (Build.cs)

### Módulos Obrigatórios
```csharp
PublicDependencyModuleNames.AddRange(new string[]
{
    "Core",
    "CoreUObject",
    "Engine",
    "UnrealEd",
    "EditorSubsystem",
    "WorldPartitionEditor",    // Para Data Layers
    "DataLayerEditor",         // Para Data Layer Editor
    "HierarchicalLOD",         // Para HLOD
    "LevelStreaming"           // Para Streaming
});
```

### Módulos Opcionais (Features Avançadas)
```csharp
PrivateDependencyModuleNames.AddRange(new string[]
{
    "AssetRegistry",
    "ContentBrowser",
    "EditorWidgets",
    "PropertyEditor",
    "Slate",
    "SlateCore",
    "ToolMenus",
    "UnrealEdMessages"
});
```

---

## ⚠️ HEADERS OBSOLETOS (NÃO USAR)

### ❌ Headers Depreciados
```cpp
// OBSOLETOS - NÃO USAR NO UE 5.6
#include "Engine/DataLayer.h"              // Depreciado
#include "WorldPartition/DataLayer.h"      // Obsoleto
#include "DataLayerSubsystem.h"            // Substituído por DataLayerManager
```

---

## 🎯 MELHORES PRÁTICAS UE 5.6

### 1. Sempre usar TObjectPtr
```cpp
// ✅ CORRETO (UE 5.6)
TObjectPtr<UDataLayerAsset> DataLayerAsset;
TObjectPtr<UDataLayerInstance> DataLayerInstance;

// ❌ OBSOLETO
UDataLayerAsset* DataLayerAsset;
UDataLayerInstance* DataLayerInstance;
```

### 2. Verificação de Null Pointers
```cpp
// ✅ CORRETO
if (IsValid(DataLayerManager))
{
    const UDataLayerInstance* Instance = DataLayerManager->GetDataLayerInstance(DataLayerAsset);
    if (IsValid(Instance))
    {
        // Usar Instance
    }
}
```

### 3. Error Handling Robusto
```cpp
// ✅ CORRETO
bool CreateDataLayerSafely(const FString& LayerName)
{
    if (LayerName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("Layer name cannot be empty"));
        return false;
    }
    
    // Implementação robusta
    return true;
}
```

---

## 📈 NOVIDADES DO UE 5.6

### 🆕 External Data Layers
- Suporte para Data Layers externos
- Melhor integração com sistemas de versionamento
- Carregamento dinâmico aprimorado

### 🆕 Performance Improvements
- Streaming otimizado
- Menor uso de memória
- Carregamento assíncrono melhorado

### 🆕 Editor Enhancements
- Interface melhorada do Data Layers Outliner
- Melhor visualização de dependências
- Ferramentas de debug aprimoradas

---

**✅ DOCUMENTAÇÃO VERIFICADA COM FONTE OFICIAL DO UE 5.6**
