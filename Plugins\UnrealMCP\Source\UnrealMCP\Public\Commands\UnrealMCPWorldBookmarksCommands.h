#pragma once

#include "CoreMinimal.h"
#include "Json.h"

/**
 * Handles World Bookmarks related MCP commands for UE 5.6
 * World Bookmarks is a new system to help navigation within a level and across multiple worlds
 * while working in the editor. This provides production-ready bookmark management functionality.
 */
class UNREALMCP_API FUnrealMCPWorldBookmarksCommands
{
public:
    FUnrealMCPWorldBookmarksCommands();

    /**
     * Handle World Bookmarks-related commands
     * @param CommandType - The type of command to handle
     * @param Params - JSON parameters for the command
     * @return JSON response with results or error
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    /**
     * Create a new world bookmark at current camera position
     * @param Params - Must include:
     *                "bookmark_name" - Name for the new bookmark
     *                "description" - Optional description for the bookmark
     *                "world_name" - Optional world name (uses current world if not specified)
     * @return JSON response with bookmark creation results
     */
    TSharedPtr<FJsonObject> HandleCreateWorldBookmark(const TSharedPtr<FJsonObject>& Params);

    /**
     * Navigate to a specific world bookmark
     * @param Params - Must include:
     *                "bookmark_name" - Name of the bookmark to navigate to
     *                "transition_speed" - Optional speed of camera transition (1.0 = default)
     * @return JSON response with navigation results
     */
    TSharedPtr<FJsonObject> HandleNavigateToBookmark(const TSharedPtr<FJsonObject>& Params);

    /**
     * List all available world bookmarks
     * @param Params - Optional parameters:
     *                "world_name" - Filter by specific world (optional)
     *                "include_details" - Include detailed bookmark information
     * @return JSON response with bookmarks list
     */
    TSharedPtr<FJsonObject> HandleListWorldBookmarks(const TSharedPtr<FJsonObject>& Params);

    /**
     * Update an existing world bookmark
     * @param Params - Must include:
     *                "bookmark_name" - Name of the bookmark to update
     *                "new_name" - New name for the bookmark (optional)
     *                "new_description" - New description (optional)
     *                "update_position" - Update to current camera position (optional)
     * @return JSON response with update results
     */
    TSharedPtr<FJsonObject> HandleUpdateWorldBookmark(const TSharedPtr<FJsonObject>& Params);

    /**
     * Delete a world bookmark
     * @param Params - Must include:
     *                "bookmark_name" - Name of the bookmark to delete
     * @return JSON response with deletion results
     */
    TSharedPtr<FJsonObject> HandleDeleteWorldBookmark(const TSharedPtr<FJsonObject>& Params);

    /**
     * Import bookmarks from a file
     * @param Params - Must include:
     *                "file_path" - Path to the bookmarks file
     *                "merge_mode" - How to handle existing bookmarks ("replace", "merge", "skip")
     * @return JSON response with import results
     */
    TSharedPtr<FJsonObject> HandleImportBookmarks(const TSharedPtr<FJsonObject>& Params);

    /**
     * Export bookmarks to a file
     * @param Params - Must include:
     *                "file_path" - Path where to save the bookmarks file
     *                "world_name" - Optional world filter
     * @return JSON response with export results
     */
    TSharedPtr<FJsonObject> HandleExportBookmarks(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create bookmark sequence for automated navigation
     * @param Params - Must include:
     *                "sequence_name" - Name for the bookmark sequence
     *                "bookmark_names" - Array of bookmark names in order
     *                "transition_time" - Time between bookmarks in seconds
     * @return JSON response with sequence creation results
     */
    TSharedPtr<FJsonObject> HandleCreateBookmarkSequence(const TSharedPtr<FJsonObject>& Params);

    /**
     * Play a bookmark sequence
     * @param Params - Must include:
     *                "sequence_name" - Name of the sequence to play
     *                "loop" - Whether to loop the sequence (optional)
     * @return JSON response with playback results
     */
    TSharedPtr<FJsonObject> HandlePlayBookmarkSequence(const TSharedPtr<FJsonObject>& Params);

private:
    // Helper structures for bookmark management
    struct FWorldBookmark
    {
        FString Name;
        FString Description;
        FString WorldName;
        FVector Location;
        FRotator Rotation;
        FDateTime CreatedTime;
        FDateTime LastModified;

        // REAL UE 5.6 World Bookmarks properties based on official documentation
        int32 BookmarkIndex;
        bool bIsActive;
    };

    struct FBookmarkSequence
    {
        FString Name;
        TArray<FString> BookmarkNames;
        float TransitionTime;
        bool bLoop;
        FString Description;
        FString WorldName;
        float DefaultDuration;
        FDateTime CreatedTime;
        FDateTime LastModified;
    };

    // Helper functions for bookmark operations
    bool ValidateBookmarkName(const FString& BookmarkName) const;
    FVector GetCurrentCameraLocation() const;
    FRotator GetCurrentCameraRotation() const;
    bool SetCameraLocationAndRotation(const FVector& Location, const FRotator& Rotation, float TransitionSpeed = 1.0f) const;
    
    // Bookmark storage and management
    TArray<FBookmarkSequence> LoadBookmarkSequences() const;
    bool SaveBookmarkSequences(const TArray<FBookmarkSequence>& Sequences) const;
    TArray<FWorldBookmark> LoadBookmarks() const;
    bool SaveBookmarks(const TArray<FWorldBookmark>& Bookmarks) const;
    FString GetBookmarksFilePath() const;
    
    // Sequence management
    TArray<FBookmarkSequence> LoadSequences() const;
    bool SaveSequences(const TArray<FBookmarkSequence>& Sequences) const;
    FString GetSequencesFilePath() const;
    
    // Current world information
    FString GetCurrentWorldName() const;
    UWorld* GetCurrentWorld() const;
};
