"""
World Partition Tools for Unreal MCP.

This module provides tools for managing World Partition in Unreal Engine 5.6.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_world_partition_tools(mcp: FastMCP):
    """Register world partition tools with the MCP server."""
    
    @mcp.tool()
    def enable_world_partition(
        ctx: Context,
        grid_size: int = 64000,
        loading_range: int = 25600,
        enable_streaming: bool = True
    ) -> Dict[str, Any]:
        """Enable World Partition for the current level.
        
        Args:
            grid_size: Size of each World Partition cell (default: 64000 units)
            loading_range: Distance for loading cells (default: 25600 units)
            enable_streaming: Enable level streaming
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("enable_world_partition", {
                "grid_size": grid_size,
                "loading_range": loading_range,
                "enable_streaming": enable_streaming
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Enable World Partition response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error enabling World Partition: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def configure_world_partition_grid(
        ctx: Context,
        grid_size: int,
        loading_range: int,
        optimize_for_large_worlds: bool = True
    ) -> Dict[str, Any]:
        """Configure World Partition grid settings.
        
        Args:
            grid_size: Size of each cell in Unreal units
            loading_range: Distance for loading cells
            optimize_for_large_worlds: Use optimizations for large worlds
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("configure_world_partition_grid", {
                "grid_size": grid_size,
                "loading_range": loading_range,
                "optimize_for_large_worlds": optimize_for_large_worlds
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Configure World Partition grid response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error configuring World Partition grid: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def set_streaming_policy(
        ctx: Context,
        policy_type: str = "Default",
        custom_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Set World Partition streaming policy.
        
        Args:
            policy_type: "Default", "ServerStreaming", "EditorStreaming"
            custom_settings: JSON object with custom policy settings
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("set_streaming_policy", {
                "policy_type": policy_type,
                "custom_settings": custom_settings
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Set streaming policy response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error setting streaming policy: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def configure_streaming_distances(
        ctx: Context,
        loading_range: float,
        unloading_range: float,
        streaming_source_radius: float = 5000.0
    ) -> Dict[str, Any]:
        """Configure streaming distances for optimal performance.
        
        Args:
            loading_range: Distance to start loading cells
            unloading_range: Distance to unload cells (should be > loading_range)
            streaming_source_radius: Radius around streaming sources
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("configure_streaming_distances", {
                "loading_range": loading_range,
                "unloading_range": unloading_range,
                "streaming_source_radius": streaming_source_radius
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Configure streaming distances response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error configuring streaming distances: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def add_streaming_source(
        ctx: Context,
        location: List[float],
        priority: int = 50,
        radius: float = 10000.0,
        source_name: str = ""
    ) -> Dict[str, Any]:
        """Add streaming source at location.
        
        Args:
            location: World location for streaming source [x, y, z]
            priority: Streaming priority (0-100)
            radius: Effective radius of streaming source
            source_name: Optional name for the streaming source
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("add_streaming_source", {
                "location": location,
                "priority": priority,
                "radius": radius,
                "source_name": source_name
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Add streaming source response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error adding streaming source: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def configure_hlod(
        ctx: Context,
        enable_hlod: bool = True,
        hlod_distance: float = 50000.0,
        max_hlod_levels: int = 3,
        simplification_ratio: float = 0.5
    ) -> Dict[str, Any]:
        """Configure HLOD settings for the world.
        
        Args:
            enable_hlod: Enable HLOD system
            hlod_distance: Distance to switch to HLOD
            max_hlod_levels: Maximum number of HLOD levels
            simplification_ratio: Mesh simplification ratio per level
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("configure_hlod", {
                "enable_hlod": enable_hlod,
                "hlod_distance": hlod_distance,
                "max_hlod_levels": max_hlod_levels,
                "simplification_ratio": simplification_ratio
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Configure HLOD response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error configuring HLOD: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def optimize_for_large_world(
        ctx: Context,
        asset_count: int,
        target_frame_rate: int = 60,
        platform_type: str = "Desktop"
    ) -> Dict[str, Any]:
        """Optimize World Partition settings for large worlds (400+ assets).
        
        Args:
            asset_count: Estimated number of assets in the world
            target_frame_rate: Target frame rate (30, 60, 120)
            platform_type: "Desktop", "Console", "Mobile"
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("optimize_for_large_world", {
                "asset_count": asset_count,
                "target_frame_rate": target_frame_rate,
                "platform_type": platform_type
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Optimize for large world response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error optimizing for large world: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def get_performance_metrics(
        ctx: Context,
        include_detailed_stats: bool = False
    ) -> Dict[str, Any]:
        """Monitor World Partition performance.

        Args:
            include_detailed_stats: Include detailed performance statistics
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}

            response = unreal.send_command("get_performance_metrics", {
                "include_detailed_stats": include_detailed_stats
            })

            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}

            logger.info(f"Get performance metrics response: {response}")
            return response.get("result", response)

        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def set_performance_budgets(
        ctx: Context,
        max_cells_per_frame: int = 4,
        max_memory_budget_mb: int = 2048,
        max_streaming_time_budget_ms: float = 5.0
    ) -> Dict[str, Any]:
        """Set performance budgets for streaming.

        Args:
            max_cells_per_frame: Maximum cells to process per frame
            max_memory_budget_mb: Maximum memory budget in MB
            max_streaming_time_budget_ms: Maximum time budget per frame in MS
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}

            response = unreal.send_command("set_performance_budgets", {
                "max_cells_per_frame": max_cells_per_frame,
                "max_memory_budget_mb": max_memory_budget_mb,
                "max_streaming_time_budget_ms": max_streaming_time_budget_ms
            })

            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}

            logger.info(f"Set performance budgets response: {response}")
            return response.get("result", response)

        except Exception as e:
            logger.error(f"Error setting performance budgets: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def get_world_partition_info(
        ctx: Context,
        include_detailed_info: bool = False
    ) -> Dict[str, Any]:
        """Get World Partition information for current world.

        Args:
            include_detailed_info: Include detailed cell information
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}

            response = unreal.send_command("get_world_partition_info", {
                "include_detailed_info": include_detailed_info
            })

            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}

            logger.info(f"Get World Partition info response: {response}")
            return response.get("result", response)

        except Exception as e:
            logger.error(f"Error getting World Partition info: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def get_loaded_cells(
        ctx: Context,
        include_actor_counts: bool = False
    ) -> Dict[str, Any]:
        """Get all loaded cells information.

        Args:
            include_actor_counts: Include actor counts per cell
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}

            response = unreal.send_command("get_loaded_cells", {
                "include_actor_counts": include_actor_counts
            })

            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}

            logger.info(f"Get loaded cells response: {response}")
            return response.get("result", response)

        except Exception as e:
            logger.error(f"Error getting loaded cells: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def get_streaming_sources(ctx: Context) -> Dict[str, Any]:
        """Get streaming sources information."""
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}

            response = unreal.send_command("get_streaming_sources", {})

            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}

            logger.info(f"Get streaming sources response: {response}")
            return response.get("result", response)

        except Exception as e:
            logger.error(f"Error getting streaming sources: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def validate_wp_configuration(
        ctx: Context,
        grid_size: int,
        loading_range: int
    ) -> Dict[str, Any]:
        """Validate World Partition configuration.

        Args:
            grid_size: Grid size to validate
            loading_range: Loading range to validate
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}

            response = unreal.send_command("validate_wp_configuration", {
                "grid_size": grid_size,
                "loading_range": loading_range
            })

            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}

            logger.info(f"Validate WP configuration response: {response}")
            return response.get("result", response)

        except Exception as e:
            logger.error(f"Error validating WP configuration: {e}")
            return {"error": str(e)}
