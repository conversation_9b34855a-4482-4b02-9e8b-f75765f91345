"""World Bookmarks Tools for Unreal MCP.

This module provides tools for managing World Bookmarks in Unreal Engine 5.6.
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_world_bookmarks_tools(mcp: FastMCP):
    """Register world bookmarks tools with the MCP server."""
    
    @mcp.tool()
    def update_world_bookmark(
        ctx: Context,
        bookmark_name: str,
        description: Optional[str] = None,
        location: Optional[List[float]] = None,
        rotation: Optional[List[float]] = None
    ) -> Dict[str, Any]:
        """Update an existing world bookmark.
        
        Args:
            bookmark_name: Name of the bookmark to update
            description: New description for the bookmark
            location: New world location [x, y, z] (optional)
            rotation: New rotation [pitch, yaw, roll] (optional)
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("update_world_bookmark", {
                "bookmark_name": bookmark_name,
                "description": description,
                "location": location,
                "rotation": rotation
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Update world bookmark response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error updating world bookmark: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def delete_world_bookmark(
        ctx: Context,
        bookmark_name: str
    ) -> Dict[str, Any]:
        """Delete a world bookmark.
        
        Args:
            bookmark_name: Name of the bookmark to delete
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("delete_world_bookmark", {
                "bookmark_name": bookmark_name
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Delete world bookmark response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error deleting world bookmark: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def import_bookmarks(
        ctx: Context,
        file_path: str,
        overwrite_existing: bool = False,
        filter_by_world: bool = True
    ) -> Dict[str, Any]:
        """Import bookmarks from an external JSON file.
        
        Args:
            file_path: Path to the JSON file containing bookmarks
            overwrite_existing: Whether to overwrite existing bookmarks
            filter_by_world: Only import bookmarks for the current world
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("import_bookmarks", {
                "file_path": file_path,
                "overwrite_existing": overwrite_existing,
                "filter_by_world": filter_by_world
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Import bookmarks response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error importing bookmarks: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def export_bookmarks(
        ctx: Context,
        file_path: str,
        current_world_only: bool = True,
        include_metadata: bool = True
    ) -> Dict[str, Any]:
        """Export bookmarks to an external JSON file.
        
        Args:
            file_path: Path where to save the JSON file
            current_world_only: Export only bookmarks from current world
            include_metadata: Include creation/modification timestamps
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("export_bookmarks", {
                "file_path": file_path,
                "current_world_only": current_world_only,
                "include_metadata": include_metadata
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Export bookmarks response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error exporting bookmarks: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def create_bookmark_sequence(
        ctx: Context,
        sequence_name: str,
        bookmark_names: List[str],
        description: Optional[str] = None,
        default_duration: float = 3.0,
        loop: bool = False
    ) -> Dict[str, Any]:
        """Create a sequence of bookmarks for automated playback.
        
        Args:
            sequence_name: Name for the new sequence
            bookmark_names: List of bookmark names in order
            description: Optional description for the sequence
            default_duration: Default time to spend at each bookmark (seconds)
            loop: Whether the sequence should loop
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("create_bookmark_sequence", {
                "sequence_name": sequence_name,
                "bookmark_names": bookmark_names,
                "description": description,
                "default_duration": default_duration,
                "loop": loop
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Create bookmark sequence response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error creating bookmark sequence: {e}")
            return {"error": str(e)}

    @mcp.tool()
    def play_bookmark_sequence(
        ctx: Context,
        sequence_name: str,
        playback_speed: float = 1.0,
        preview_mode: bool = False,
        start_index: int = 0,
        end_index: Optional[int] = None
    ) -> Dict[str, Any]:
        """Play a bookmark sequence.
        
        Args:
            sequence_name: Name of the sequence to play
            playback_speed: Speed multiplier for playback (1.0 = normal)
            preview_mode: If true, only return sequence info without playing
            start_index: Index of bookmark to start from (0-based)
            end_index: Index of bookmark to end at (optional)
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.warning("Failed to connect to Unreal Engine")
                return {"error": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("play_bookmark_sequence", {
                "sequence_name": sequence_name,
                "playback_speed": playback_speed,
                "preview_mode": preview_mode,
                "start_index": start_index,
                "end_index": end_index
            })
            
            if not response:
                logger.warning("No response from Unreal Engine")
                return {"error": "No response from Unreal Engine"}
                
            logger.info(f"Play bookmark sequence response: {response}")
            return response.get("result", response)
            
        except Exception as e:
            logger.error(f"Error playing bookmark sequence: {e}")
            return {"error": str(e)}

    logger.info("World Bookmarks tools registered successfully")