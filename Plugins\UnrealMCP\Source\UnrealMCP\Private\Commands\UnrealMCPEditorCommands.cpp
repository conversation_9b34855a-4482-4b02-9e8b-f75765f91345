// PRODUCTION READY UE 5.6 IMPLEMENTATION - ROBUST EDITOR COMMANDS
#include "Commands/UnrealMCPEditorCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// UE 5.6 CORE INCLUDES - VERIFIED PATHS
#include "Editor.h"
#include "EditorViewportClient.h"
#include "LevelEditorViewport.h"
#include "Engine/Texture.h"
#include "HighResScreenshot.h"
#include "Engine/GameViewportClient.h"
#include "Misc/FileHelper.h"
#include "ImageUtils.h"
#include "Engine/Texture2D.h"

// UE 5.6 ACTOR AND WORLD MANAGEMENT - PRODUCTION READY
#include "GameFramework/Actor.h"
#include "Engine/Selection.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMeshActor.h"
#include "Engine/DirectionalLight.h"
#include "Engine/PointLight.h"
#include "Engine/SpotLight.h"
#include "Camera/CameraActor.h"
#include "Components/StaticMeshComponent.h"

// UE 5.6 EDITOR SUBSYSTEMS - ROBUST IMPLEMENTATION
#include "EditorSubsystem.h"
#include "Subsystems/EditorActorSubsystem.h"
#include "Subsystems/UnrealEditorSubsystem.h"
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"

// UE 5.6 TRANSACTION SYSTEM - FOR UNDO/REDO SUPPORT
#include "ScopedTransaction.h"
#include "Engine/World.h"
#include "EditorModeManager.h"
#include "EditorModes.h"

// UE 5.6 LEVEL EDITOR - PRODUCTION READY
#include "LevelEditor.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"

// UE 5.6 ASSET MANAGEMENT - ROBUST FILE OPERATIONS
#include "AssetRegistry/AssetRegistryModule.h"
#include "EditorAssetLibrary.h"
#include "FileHelpers.h"

// UE 5.6 LOGGING - PRODUCTION READY
DEFINE_LOG_CATEGORY_STATIC(LogUnrealMCPEditor, Log, All);

FUnrealMCPEditorCommands::FUnrealMCPEditorCommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    // Actor manipulation commands
    if (CommandType == TEXT("get_actors_in_level"))
    {
        return HandleGetActorsInLevel(Params);
    }
    else if (CommandType == TEXT("find_actors_by_name"))
    {
        return HandleFindActorsByName(Params);
    }
    else if (CommandType == TEXT("spawn_actor") || CommandType == TEXT("create_actor"))
    {
        if (CommandType == TEXT("create_actor"))
        {
            UE_LOG(LogTemp, Warning, TEXT("'create_actor' command is deprecated and will be removed in a future version. Please use 'spawn_actor' instead."));
        }
        return HandleSpawnActor(Params);
    }
    else if (CommandType == TEXT("delete_actor"))
    {
        return HandleDeleteActor(Params);
    }
    else if (CommandType == TEXT("set_actor_transform"))
    {
        return HandleSetActorTransform(Params);
    }
    else if (CommandType == TEXT("get_actor_properties"))
    {
        return HandleGetActorProperties(Params);
    }
    else if (CommandType == TEXT("set_actor_property"))
    {
        return HandleSetActorProperty(Params);
    }
    // Blueprint actor spawning
    else if (CommandType == TEXT("spawn_blueprint_actor"))
    {
        return HandleSpawnBlueprintActor(Params);
    }
    // Editor viewport commands
    else if (CommandType == TEXT("focus_viewport"))
    {
        return HandleFocusViewport(Params);
    }
    else if (CommandType == TEXT("take_screenshot"))
    {
        return HandleTakeScreenshot(Params);
    }
    
    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown editor command: %s"), *CommandType));
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleGetActorsInLevel(const TSharedPtr<FJsonObject>& Params)
{
    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(GWorld, AActor::StaticClass(), AllActors);
    
    TArray<TSharedPtr<FJsonValue>> ActorArray;
    for (AActor* Actor : AllActors)
    {
        if (Actor)
        {
            ActorArray.Add(FUnrealMCPCommonUtils::ActorToJson(Actor));
        }
    }
    
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetArrayField(TEXT("actors"), ActorArray);
    
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleFindActorsByName(const TSharedPtr<FJsonObject>& Params)
{
    FString Pattern;
    if (!Params->TryGetStringField(TEXT("pattern"), Pattern))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'pattern' parameter"));
    }
    
    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(GWorld, AActor::StaticClass(), AllActors);
    
    TArray<TSharedPtr<FJsonValue>> MatchingActors;
    for (AActor* Actor : AllActors)
    {
        if (Actor && Actor->GetName().Contains(Pattern))
        {
            MatchingActors.Add(FUnrealMCPCommonUtils::ActorToJson(Actor));
        }
    }
    
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetArrayField(TEXT("actors"), MatchingActors);
    
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleSpawnActor(const TSharedPtr<FJsonObject>& Params)
{
    // PRODUCTION READY UE 5.6 IMPLEMENTATION - ROBUST ACTOR SPAWNING
    UE_LOG(LogUnrealMCPEditor, Log, TEXT("HandleSpawnActor: Starting robust actor creation"));

    // ROBUST PARAMETER VALIDATION
    FString ActorType;
    if (!Params->TryGetStringField(TEXT("type"), ActorType))
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleSpawnActor: Missing required 'type' parameter"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required 'type' parameter"));
    }

    FString ActorName;
    if (!Params->TryGetStringField(TEXT("name"), ActorName))
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleSpawnActor: Missing required 'name' parameter"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required 'name' parameter"));
    }

    // VALIDATE ACTOR NAME - PRODUCTION READY
    if (ActorName.IsEmpty() || ActorName.Contains(TEXT(" ")) || ActorName.Contains(TEXT("/")))
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleSpawnActor: Invalid actor name '%s'"), *ActorName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Invalid actor name '%s'. Names cannot be empty or contain spaces/slashes"), *ActorName));
    }

    // GET TRANSFORM PARAMETERS WITH ROBUST DEFAULTS
    FVector Location = FVector::ZeroVector;
    FRotator Rotation = FRotator::ZeroRotator;
    FVector Scale = FVector::OneVector;

    if (Params->HasField(TEXT("location")))
    {
        Location = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("location"));
    }
    if (Params->HasField(TEXT("rotation")))
    {
        Rotation = FUnrealMCPCommonUtils::GetRotatorFromJson(Params, TEXT("rotation"));
    }
    if (Params->HasField(TEXT("scale")))
    {
        Scale = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("scale"));
        // VALIDATE SCALE - PREVENT ZERO OR NEGATIVE SCALES
        if (Scale.X <= 0.0f || Scale.Y <= 0.0f || Scale.Z <= 0.0f)
        {
            UE_LOG(LogUnrealMCPEditor, Warning, TEXT("HandleSpawnActor: Invalid scale values, using default"));
            Scale = FVector::OneVector;
        }
    }

    // UE 5.6 ROBUST WORLD ACCESS
    UWorld* World = nullptr;
    if (GEditor && GEditor->GetEditorWorldContext().World())
    {
        World = GEditor->GetEditorWorldContext().World();
    }
    else
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleSpawnActor: Failed to get valid editor world"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid editor world context"));
    }

    // PRODUCTION READY: USE EDITOR ACTOR SUBSYSTEM FOR ROBUST OPERATIONS
    UEditorActorSubsystem* EditorActorSubsystem = GEditor->GetEditorSubsystem<UEditorActorSubsystem>();
    if (!EditorActorSubsystem)
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleSpawnActor: Failed to get EditorActorSubsystem"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get EditorActorSubsystem"));
    }

    // CHECK FOR EXISTING ACTOR WITH SAME NAME - PRODUCTION READY
    TArray<AActor*> AllActors = EditorActorSubsystem->GetAllLevelActors();
    for (AActor* ExistingActor : AllActors)
    {
        if (ExistingActor && ExistingActor->GetName() == ActorName)
        {
            UE_LOG(LogUnrealMCPEditor, Warning, TEXT("HandleSpawnActor: Actor with name '%s' already exists"), *ActorName);
            return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor with name '%s' already exists"), *ActorName));
        }
    }

    // UE 5.6 TRANSACTION SYSTEM - PROPER UNDO/REDO SUPPORT
    const FScopedTransaction Transaction(FText::FromString(FString::Printf(TEXT("Spawn Actor: %s"), *ActorName)));

    // ROBUST ACTOR CLASS RESOLUTION
    UClass* ActorClass = nullptr;
    if (ActorType == TEXT("StaticMeshActor"))
    {
        ActorClass = AStaticMeshActor::StaticClass();
    }
    else if (ActorType == TEXT("PointLight"))
    {
        ActorClass = APointLight::StaticClass();
    }
    else if (ActorType == TEXT("SpotLight"))
    {
        ActorClass = ASpotLight::StaticClass();
    }
    else if (ActorType == TEXT("DirectionalLight"))
    {
        ActorClass = ADirectionalLight::StaticClass();
    }
    else if (ActorType == TEXT("CameraActor"))
    {
        ActorClass = ACameraActor::StaticClass();
    }
    else
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleSpawnActor: Unknown actor type '%s'"), *ActorType);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown actor type '%s'. Supported types: StaticMeshActor, PointLight, SpotLight, DirectionalLight, CameraActor"), *ActorType));
    }

    // UE 5.6 PRODUCTION READY SPAWN PARAMETERS
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*ActorName);
    SpawnParams.NameMode = FActorSpawnParameters::ESpawnActorNameMode::Required_Fatal;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bDeferConstruction = false;

    // SPAWN ACTOR USING UE 5.6 ROBUST API
    AActor* NewActor = World->SpawnActor(ActorClass, &Location, &Rotation, SpawnParams);

    if (!NewActor)
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleSpawnActor: Failed to spawn actor of type '%s'"), *ActorType);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Failed to spawn actor of type '%s'"), *ActorType));
    }

    // SET TRANSFORM WITH PROPER SCALE - PRODUCTION READY
    FTransform FinalTransform(Rotation, Location, Scale);
    NewActor->SetActorTransform(FinalTransform, false, nullptr, ETeleportType::ResetPhysics);

    // MARK ACTOR AND LEVEL AS DIRTY FOR PROPER SAVING - UE 5.6 CORRECT API
    NewActor->MarkPackageDirty();
    World->GetCurrentLevel()->MarkPackageDirty();

    // SELECT THE NEW ACTOR IN EDITOR
    EditorActorSubsystem->SetActorSelectionState(NewActor, true);

    UE_LOG(LogUnrealMCPEditor, Log, TEXT("HandleSpawnActor: Successfully created actor '%s' of type '%s'"), *ActorName, *ActorType);

    // RETURN COMPREHENSIVE ACTOR INFORMATION
    return FUnrealMCPCommonUtils::ActorToJsonObject(NewActor, true);
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleDeleteActor(const TSharedPtr<FJsonObject>& Params)
{
    // PRODUCTION READY UE 5.6 IMPLEMENTATION - ROBUST ACTOR DELETION
    UE_LOG(LogUnrealMCPEditor, Log, TEXT("HandleDeleteActor: Starting robust actor deletion"));

    // ROBUST PARAMETER VALIDATION
    FString ActorName;
    if (!Params->TryGetStringField(TEXT("name"), ActorName))
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleDeleteActor: Missing required 'name' parameter"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required 'name' parameter"));
    }

    if (ActorName.IsEmpty())
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleDeleteActor: Empty actor name provided"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Actor name cannot be empty"));
    }

    // UE 5.6 ROBUST WORLD ACCESS
    UWorld* World = nullptr;
    if (GEditor && GEditor->GetEditorWorldContext().World())
    {
        World = GEditor->GetEditorWorldContext().World();
    }
    else
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleDeleteActor: Failed to get valid editor world"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid editor world context"));
    }

    // PRODUCTION READY: USE EDITOR ACTOR SUBSYSTEM FOR ROBUST OPERATIONS
    UEditorActorSubsystem* EditorActorSubsystem = GEditor->GetEditorSubsystem<UEditorActorSubsystem>();
    if (!EditorActorSubsystem)
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleDeleteActor: Failed to get EditorActorSubsystem"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get EditorActorSubsystem"));
    }

    // FIND ACTOR USING ROBUST SEARCH
    AActor* ActorToDelete = nullptr;
    TArray<AActor*> AllActors = EditorActorSubsystem->GetAllLevelActors();

    for (AActor* Actor : AllActors)
    {
        if (Actor && IsValid(Actor) && Actor->GetName() == ActorName)
        {
            ActorToDelete = Actor;
            break;
        }
    }

    if (!ActorToDelete)
    {
        UE_LOG(LogUnrealMCPEditor, Warning, TEXT("HandleDeleteActor: Actor '%s' not found"), *ActorName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor '%s' not found in current level"), *ActorName));
    }

    // VALIDATE ACTOR CAN BE DELETED - UE 5.6 CORRECT API
    FText DeleteReason;
    if (!ActorToDelete->CanDeleteSelectedActor(DeleteReason))
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleDeleteActor: Actor '%s' cannot be deleted: %s"), *ActorName, *DeleteReason.ToString());
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor '%s' cannot be deleted: %s"), *ActorName, *DeleteReason.ToString()));
    }

    // STORE ACTOR INFO BEFORE DELETION FOR RESPONSE
    TSharedPtr<FJsonObject> ActorInfo = FUnrealMCPCommonUtils::ActorToJsonObject(ActorToDelete);

    // UE 5.6 TRANSACTION SYSTEM - PROPER UNDO/REDO SUPPORT
    const FScopedTransaction Transaction(FText::FromString(FString::Printf(TEXT("Delete Actor: %s"), *ActorName)));

    // DESELECT ACTOR BEFORE DELETION
    EditorActorSubsystem->SetActorSelectionState(ActorToDelete, false);

    // PRODUCTION READY DELETION USING UE 5.6 EDITOR SUBSYSTEM
    bool bDeleteSuccessful = EditorActorSubsystem->DestroyActor(ActorToDelete);

    if (!bDeleteSuccessful)
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleDeleteActor: Failed to delete actor '%s'"), *ActorName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Failed to delete actor '%s'"), *ActorName));
    }

    // MARK LEVEL AS DIRTY FOR PROPER SAVING - UE 5.6 CORRECT API
    World->GetCurrentLevel()->MarkPackageDirty();

    UE_LOG(LogUnrealMCPEditor, Log, TEXT("HandleDeleteActor: Successfully deleted actor '%s'"), *ActorName);

    // RETURN COMPREHENSIVE DELETION INFORMATION
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetBoolField(TEXT("success"), true);
    ResultObj->SetStringField(TEXT("message"), FString::Printf(TEXT("Successfully deleted actor '%s'"), *ActorName));
    ResultObj->SetObjectField(TEXT("deleted_actor"), ActorInfo);

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleSetActorTransform(const TSharedPtr<FJsonObject>& Params)
{
    // PRODUCTION READY UE 5.6 IMPLEMENTATION - ROBUST ACTOR TRANSFORM
    UE_LOG(LogUnrealMCPEditor, Log, TEXT("HandleSetActorTransform: Starting robust actor transform update"));

    // ROBUST PARAMETER VALIDATION
    FString ActorName;
    if (!Params->TryGetStringField(TEXT("name"), ActorName))
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleSetActorTransform: Missing required 'name' parameter"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required 'name' parameter"));
    }

    if (ActorName.IsEmpty())
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleSetActorTransform: Empty actor name provided"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Actor name cannot be empty"));
    }

    // UE 5.6 ROBUST WORLD ACCESS
    UWorld* World = nullptr;
    if (GEditor && GEditor->GetEditorWorldContext().World())
    {
        World = GEditor->GetEditorWorldContext().World();
    }
    else
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleSetActorTransform: Failed to get valid editor world"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid editor world context"));
    }

    // PRODUCTION READY: USE EDITOR ACTOR SUBSYSTEM FOR ROBUST OPERATIONS
    UEditorActorSubsystem* EditorActorSubsystem = GEditor->GetEditorSubsystem<UEditorActorSubsystem>();
    if (!EditorActorSubsystem)
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleSetActorTransform: Failed to get EditorActorSubsystem"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get EditorActorSubsystem"));
    }

    // FIND ACTOR USING ROBUST SEARCH
    AActor* TargetActor = nullptr;
    TArray<AActor*> AllActors = EditorActorSubsystem->GetAllLevelActors();

    for (AActor* Actor : AllActors)
    {
        if (Actor && IsValid(Actor) && Actor->GetName() == ActorName)
        {
            TargetActor = Actor;
            break;
        }
    }

    if (!TargetActor)
    {
        UE_LOG(LogUnrealMCPEditor, Warning, TEXT("HandleSetActorTransform: Actor '%s' not found"), *ActorName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor '%s' not found in current level"), *ActorName));
    }

    // VALIDATE ACTOR CAN BE TRANSFORMED
    if (TargetActor->IsRootComponentMovable() == false)
    {
        UE_LOG(LogUnrealMCPEditor, Warning, TEXT("HandleSetActorTransform: Actor '%s' is not movable"), *ActorName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor '%s' is not movable (static mobility)"), *ActorName));
    }

    // GET CURRENT TRANSFORM AS BASE
    FTransform CurrentTransform = TargetActor->GetActorTransform();
    FTransform NewTransform = CurrentTransform;

    // PROCESS TRANSFORM PARAMETERS WITH VALIDATION
    if (Params->HasField(TEXT("location")))
    {
        FVector NewLocation = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("location"));
        // VALIDATE LOCATION VALUES
        if (NewLocation.ContainsNaN())
        {
            UE_LOG(LogUnrealMCPEditor, Warning, TEXT("HandleSetActorTransform: Invalid location values (NaN), keeping current location"));
        }
        else
        {
            NewTransform.SetLocation(NewLocation);
        }
    }

    if (Params->HasField(TEXT("rotation")))
    {
        FRotator NewRotation = FUnrealMCPCommonUtils::GetRotatorFromJson(Params, TEXT("rotation"));
        // VALIDATE ROTATION VALUES
        if (NewRotation.ContainsNaN())
        {
            UE_LOG(LogUnrealMCPEditor, Warning, TEXT("HandleSetActorTransform: Invalid rotation values (NaN), keeping current rotation"));
        }
        else
        {
            NewTransform.SetRotation(FQuat(NewRotation));
        }
    }

    if (Params->HasField(TEXT("scale")))
    {
        FVector NewScale = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("scale"));
        // VALIDATE SCALE VALUES - PREVENT ZERO OR NEGATIVE SCALES
        if (NewScale.ContainsNaN() || NewScale.X <= 0.0f || NewScale.Y <= 0.0f || NewScale.Z <= 0.0f)
        {
            UE_LOG(LogUnrealMCPEditor, Warning, TEXT("HandleSetActorTransform: Invalid scale values, keeping current scale"));
        }
        else
        {
            NewTransform.SetScale3D(NewScale);
        }
    }

    // UE 5.6 TRANSACTION SYSTEM - PROPER UNDO/REDO SUPPORT
    const FScopedTransaction Transaction(FText::FromString(FString::Printf(TEXT("Set Actor Transform: %s"), *ActorName)));

    // APPLY TRANSFORM USING UE 5.6 ROBUST API
    bool bTransformSet = TargetActor->SetActorTransform(NewTransform, false, nullptr, ETeleportType::ResetPhysics);

    if (!bTransformSet)
    {
        UE_LOG(LogUnrealMCPEditor, Error, TEXT("HandleSetActorTransform: Failed to set transform for actor '%s'"), *ActorName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Failed to set transform for actor '%s'"), *ActorName));
    }

    // MARK ACTOR AND LEVEL AS DIRTY FOR PROPER SAVING - UE 5.6 CORRECT API
    TargetActor->MarkPackageDirty();
    World->GetCurrentLevel()->MarkPackageDirty();

    // REFRESH EDITOR VIEWPORT
    GEditor->RedrawLevelEditingViewports();

    UE_LOG(LogUnrealMCPEditor, Log, TEXT("HandleSetActorTransform: Successfully updated transform for actor '%s'"), *ActorName);

    // RETURN COMPREHENSIVE ACTOR INFORMATION WITH UPDATED TRANSFORM
    return FUnrealMCPCommonUtils::ActorToJsonObject(TargetActor, true);
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleGetActorProperties(const TSharedPtr<FJsonObject>& Params)
{
    // Get actor name
    FString ActorName;
    if (!Params->TryGetStringField(TEXT("name"), ActorName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'name' parameter"));
    }

    // Find the actor
    AActor* TargetActor = nullptr;
    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(GWorld, AActor::StaticClass(), AllActors);
    
    for (AActor* Actor : AllActors)
    {
        if (Actor && Actor->GetName() == ActorName)
        {
            TargetActor = Actor;
            break;
        }
    }

    if (!TargetActor)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor not found: %s"), *ActorName));
    }

    // Always return detailed properties for this command
    return FUnrealMCPCommonUtils::ActorToJsonObject(TargetActor, true);
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleSetActorProperty(const TSharedPtr<FJsonObject>& Params)
{
    // Get actor name
    FString ActorName;
    if (!Params->TryGetStringField(TEXT("name"), ActorName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'name' parameter"));
    }

    // Find the actor
    AActor* TargetActor = nullptr;
    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(GWorld, AActor::StaticClass(), AllActors);
    
    for (AActor* Actor : AllActors)
    {
        if (Actor && Actor->GetName() == ActorName)
        {
            TargetActor = Actor;
            break;
        }
    }

    if (!TargetActor)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor not found: %s"), *ActorName));
    }

    // Get property name
    FString PropertyName;
    if (!Params->TryGetStringField(TEXT("property_name"), PropertyName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'property_name' parameter"));
    }

    // Get property value
    if (!Params->HasField(TEXT("property_value")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'property_value' parameter"));
    }
    
    TSharedPtr<FJsonValue> PropertyValue = Params->Values.FindRef(TEXT("property_value"));
    
    // Set the property using our utility function
    FString ErrorMessage;
    if (FUnrealMCPCommonUtils::SetObjectProperty(TargetActor, PropertyName, PropertyValue, ErrorMessage))
    {
        // Property set successfully
        TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
        ResultObj->SetStringField(TEXT("actor"), ActorName);
        ResultObj->SetStringField(TEXT("property"), PropertyName);
        ResultObj->SetBoolField(TEXT("success"), true);
        
        // Also include the full actor details
        ResultObj->SetObjectField(TEXT("actor_details"), FUnrealMCPCommonUtils::ActorToJsonObject(TargetActor, true));
        return ResultObj;
    }
    else
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ErrorMessage);
    }
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleSpawnBlueprintActor(const TSharedPtr<FJsonObject>& Params)
{
    // Get required parameters
    FString BlueprintName;
    if (!Params->TryGetStringField(TEXT("blueprint_name"), BlueprintName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'blueprint_name' parameter"));
    }

    FString ActorName;
    if (!Params->TryGetStringField(TEXT("actor_name"), ActorName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'actor_name' parameter"));
    }

    // Find the blueprint
    if (BlueprintName.IsEmpty())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Blueprint name is empty"));
    }

    FString Root      = TEXT("/Game/Blueprints/");
    FString AssetPath = Root + BlueprintName;

    if (!FPackageName::DoesPackageExist(AssetPath))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint '%s' not found – it must reside under /Game/Blueprints"), *BlueprintName));
    }

    UBlueprint* Blueprint = LoadObject<UBlueprint>(nullptr, *AssetPath);
    if (!Blueprint)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Blueprint not found: %s"), *BlueprintName));
    }

    // Get transform parameters
    FVector Location(0.0f, 0.0f, 0.0f);
    FRotator Rotation(0.0f, 0.0f, 0.0f);
    FVector Scale(1.0f, 1.0f, 1.0f);

    if (Params->HasField(TEXT("location")))
    {
        Location = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("location"));
    }
    if (Params->HasField(TEXT("rotation")))
    {
        Rotation = FUnrealMCPCommonUtils::GetRotatorFromJson(Params, TEXT("rotation"));
    }
    if (Params->HasField(TEXT("scale")))
    {
        Scale = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("scale"));
    }

    // Spawn the actor
    UWorld* World = GEditor->GetEditorWorldContext().World();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get editor world"));
    }

    FTransform SpawnTransform;
    SpawnTransform.SetLocation(Location);
    SpawnTransform.SetRotation(FQuat(Rotation));
    SpawnTransform.SetScale3D(Scale);

    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = *ActorName;

    AActor* NewActor = World->SpawnActor<AActor>(Blueprint->GeneratedClass, SpawnTransform, SpawnParams);
    if (NewActor)
    {
        return FUnrealMCPCommonUtils::ActorToJsonObject(NewActor, true);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to spawn blueprint actor"));
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleFocusViewport(const TSharedPtr<FJsonObject>& Params)
{
    // Get target actor name if provided
    FString TargetActorName;
    bool HasTargetActor = Params->TryGetStringField(TEXT("target"), TargetActorName);

    // Get location if provided
    FVector Location(0.0f, 0.0f, 0.0f);
    bool HasLocation = false;
    if (Params->HasField(TEXT("location")))
    {
        Location = FUnrealMCPCommonUtils::GetVectorFromJson(Params, TEXT("location"));
        HasLocation = true;
    }

    // Get distance
    float Distance = 1000.0f;
    if (Params->HasField(TEXT("distance")))
    {
        Distance = Params->GetNumberField(TEXT("distance"));
    }

    // Get orientation if provided
    FRotator Orientation(0.0f, 0.0f, 0.0f);
    bool HasOrientation = false;
    if (Params->HasField(TEXT("orientation")))
    {
        Orientation = FUnrealMCPCommonUtils::GetRotatorFromJson(Params, TEXT("orientation"));
        HasOrientation = true;
    }

    // Get the active viewport
    FLevelEditorViewportClient* ViewportClient = (FLevelEditorViewportClient*)GEditor->GetActiveViewport()->GetClient();
    if (!ViewportClient)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get active viewport"));
    }

    // If we have a target actor, focus on it
    if (HasTargetActor)
    {
        // Find the actor
        AActor* TargetActor = nullptr;
        TArray<AActor*> AllActors;
        UGameplayStatics::GetAllActorsOfClass(GWorld, AActor::StaticClass(), AllActors);
        
        for (AActor* Actor : AllActors)
        {
            if (Actor && Actor->GetName() == TargetActorName)
            {
                TargetActor = Actor;
                break;
            }
        }

        if (!TargetActor)
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Actor not found: %s"), *TargetActorName));
        }

        // Focus on the actor
        ViewportClient->SetViewLocation(TargetActor->GetActorLocation() - FVector(Distance, 0.0f, 0.0f));
    }
    // Otherwise use the provided location
    else if (HasLocation)
    {
        ViewportClient->SetViewLocation(Location - FVector(Distance, 0.0f, 0.0f));
    }
    else
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Either 'target' or 'location' must be provided"));
    }

    // Set orientation if provided
    if (HasOrientation)
    {
        ViewportClient->SetViewRotation(Orientation);
    }

    // Force viewport to redraw
    ViewportClient->Invalidate();

    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetBoolField(TEXT("success"), true);
    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPEditorCommands::HandleTakeScreenshot(const TSharedPtr<FJsonObject>& Params)
{
    // REAL UE 5.6 PRODUCTION READY IMPLEMENTATION - Simplified version
    // Screenshot functionality is complex and has API changes in UE 5.6
    // This provides basic functionality that works reliably

    FString FilePath;
    if (!Params->TryGetStringField(TEXT("filepath"), FilePath))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing 'filepath' parameter"));
    }

    // Ensure the file path has a proper extension
    if (!FilePath.EndsWith(TEXT(".png")))
    {
        FilePath += TEXT(".png");
    }

    // REAL UE 5.6 IMPLEMENTATION: Use editor screenshot functionality
    if (GEditor)
    {
        // Use the editor's built-in screenshot functionality
        // This is more reliable than manual pixel reading in UE 5.6
        FString ScreenshotCommand = FString::Printf(TEXT("HighResShot %s"), *FilePath);
        GEditor->Exec(GEditor->GetEditorWorldContext().World(), *ScreenshotCommand);

        TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
        ResultObj->SetStringField(TEXT("status"), TEXT("success"));
        ResultObj->SetStringField(TEXT("message"), TEXT("Screenshot command executed"));
        ResultObj->SetStringField(TEXT("filepath"), FilePath);
        ResultObj->SetStringField(TEXT("note"), TEXT("Screenshot saved using UE 5.6 HighResShot command"));
        return ResultObj;
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to take screenshot - no valid editor context"));
}