"C:/prototipo/AURACRON/Intermediate/Build/Win64/x64/AURACRONEditor/Development/Engine/SharedPCH.Engine.Project.ValApi.ValExpApi.Cpp20.cpp"
/I "."
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Engine/UHT"
/I "Runtime/Engine/Classes"
/I "Runtime/Engine/Public"
/I "Runtime/Engine/Internal"
/I "Runtime/Core/Public"
/I "Runtime/Core/Internal"
/I "Runtime/TraceLog/Public"
/I "Runtime/AutoRTFM/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ImageCore/UHT"
/I "Runtime/ImageCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CoreOnline/UHT"
/I "Runtime/CoreOnline/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CoreUObject/UHT"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CoreUObject/VerseVMBytecode"
/I "Runtime/CoreUObject/Public"
/I "Runtime/CoreUObject/Internal"
/I "Runtime/CorePreciseFP/Public"
/I "Runtime/CorePreciseFP/Internal"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/FieldNotification/UHT"
/I "Runtime/FieldNotification/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/NetCore/UHT"
/I "Runtime/Net/Core/Classes"
/I "Runtime/Net/Core/Public"
/I "Runtime/Net/Common/Public"
/I "Runtime/Json/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/JsonUtilities/UHT"
/I "Runtime/JsonUtilities/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SlateCore/UHT"
/I "Runtime/SlateCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DeveloperSettings/UHT"
/I "Runtime/DeveloperSettings/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InputCore/UHT"
/I "Runtime/InputCore/Classes"
/I "Runtime/InputCore/Public"
/I "Runtime/ApplicationCore/Public"
/I "Runtime/ApplicationCore/Internal"
/I "Runtime/RHI/Public"
/I "Runtime/RHI/Internal"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Slate/UHT"
/I "Runtime/Slate/Public"
/I "Runtime/ImageWrapper/Public"
/I "Runtime/Messaging/Public"
/I "Runtime/MessagingCommon/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/RenderCore/UHT"
/I "Runtime/RenderCore/Public"
/I "Runtime/RenderCore/Internal"
/I "Runtime/OpenGLDrv/Public"
/I "Runtime/Analytics/AnalyticsET/Public"
/I "Runtime/Analytics/Analytics/Public"
/I "Runtime/Sockets/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AssetRegistry/UHT"
/I "Runtime/AssetRegistry/Public"
/I "Runtime/AssetRegistry/Internal"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EngineMessages/UHT"
/I "Runtime/EngineMessages/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EngineSettings/UHT"
/I "Runtime/EngineSettings/Classes"
/I "Runtime/EngineSettings/Public"
/I "Runtime/SynthBenchmark/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GameplayTags/UHT"
/I "Runtime/GameplayTags/Classes"
/I "Runtime/GameplayTags/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PacketHandler/UHT"
/I "Runtime/PacketHandlers/PacketHandler/Classes"
/I "Runtime/PacketHandlers/PacketHandler/Public"
/I "Runtime/PacketHandlers/ReliabilityHandlerComponent/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioPlatformConfiguration/UHT"
/I "Runtime/AudioPlatformConfiguration/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MeshDescription/UHT"
/I "Runtime/MeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StaticMeshDescription/UHT"
/I "Runtime/StaticMeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SkeletalMeshDescription/UHT"
/I "Runtime/SkeletalMeshDescription/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationCore/UHT"
/I "Runtime/AnimationCore/Public"
/I "Runtime/PakFile/Public"
/I "Runtime/PakFile/Internal"
/I "Runtime/RSA/Public"
/I "Runtime/NetworkReplayStreaming/NetworkReplayStreaming/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PhysicsCore/UHT"
/I "Runtime/PhysicsCore/Public"
/I "Runtime/Experimental/ChaosCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Chaos/UHT"
/I "Runtime/Experimental/Chaos/Public"
/I "Runtime/Experimental/Voronoi/Public"
/I "Runtime/GeometryCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ChaosVDRuntime/UHT"
/I "Runtime/Experimental/ChaosVisualDebugger/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/NNE/UHT"
/I "Runtime/NNE/Public"
/I "Runtime/SignalProcessing/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StateStream/UHT"
/I "Runtime/StateStream/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioExtensions/UHT"
/I "Runtime/AudioExtensions/Public"
/I "Runtime/AudioMixerCore/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioMixer/UHT"
/I "Runtime/AudioMixer/Classes"
/I "Runtime/AudioMixer/Public"
/I "Developer/TargetPlatform/Public"
/I "Developer/TextureFormat/Public"
/I "Developer/DesktopPlatform/Public"
/I "Developer/DesktopPlatform/Internal"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioLinkEngine/UHT"
/I "Runtime/AudioLink/AudioLinkEngine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioLinkCore/UHT"
/I "Runtime/AudioLink/AudioLinkCore/Public"
/I "Runtime/CookOnTheFly/Internal"
/I "Runtime/Networking/Public"
/I "Runtime/Experimental/IoStore/OnDemandCore/Public"
/I "Runtime/Experimental/IoStore/OnDemandCore/Internal"
/I "Developer/TextureBuildUtilities/Public"
/I "Developer/Horde/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ClothSysRuntimeIntrfc/UHT"
/I "Runtime/ClothingSystemRuntimeInterface/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/IrisCore/UHT"
/I "Runtime/Experimental/Iris/Core/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MovieSceneCapture/UHT"
/I "Runtime/MovieSceneCapture/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Renderer/UHT"
/I "Runtime/Renderer/Public"
/I "Runtime/Renderer/Internal"
/I "../Shaders/Public"
/I "../Shaders/Shared"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/TypedElementFramework/UHT"
/I "Runtime/TypedElementFramework/Tests"
/I "Runtime/TypedElementFramework/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/TypedElementRuntime/UHT"
/I "Runtime/TypedElementRuntime/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationDataController/UHT"
/I "Developer/AnimationDataController/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationBlueprintEditor/UHT"
/I "Editor/AnimationBlueprintEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Kismet/UHT"
/I "Editor/Kismet/Classes"
/I "Editor/Kismet/Public"
/I "Editor/Kismet/Internal"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Persona/UHT"
/I "Editor/Persona/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SkeletonEditor/UHT"
/I "Editor/SkeletonEditor/Public"
/I "Developer/AnimationWidgets/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ToolWidgets/UHT"
/I "Developer/ToolWidgets/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ToolMenus/UHT"
/I "Developer/ToolMenus/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationEditor/UHT"
/I "Editor/AnimationEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AdvancedPreviewScene/UHT"
/I "Editor/AdvancedPreviewScene/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PropertyEditor/UHT"
/I "Editor/PropertyEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EditorConfig/UHT"
/I "Editor/EditorConfig/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EditorFramework/UHT"
/I "Editor/EditorFramework/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/EditorSubsystem/UHT"
/I "Editor/EditorSubsystem/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InteractiveToolsFramework/UHT"
/I "Runtime/InteractiveToolsFramework/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/UnrealEd/UHT"
/I "Programs/UnrealLightmass/Public"
/I "Editor/UnrealEd/Classes"
/I "Editor/UnrealEd/Public"
/I "Editor/AssetTagsEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/CollectionManager/UHT"
/I "Developer/CollectionManager/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ContentBrowser/UHT"
/I "Editor/ContentBrowser/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AssetTools/UHT"
/I "Developer/AssetTools/Public"
/I "Developer/AssetTools/Internal"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AssetDefinition/UHT"
/I "Editor/AssetDefinition/Public"
/I "Developer/Merge/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ContentBrowserData/UHT"
/I "Editor/ContentBrowserData/Public"
/I "Runtime/Projects/Public"
/I "Runtime/Projects/Internal"
/I "Developer/MeshUtilities/Public"
/I "Developer/MeshMergeUtilities/Public"
/I "Developer/MeshReductionInterface/Public"
/I "Runtime/RawMesh/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MaterialUtilities/UHT"
/I "Developer/MaterialUtilities/Public"
/I "Editor/KismetCompiler/Public"
/I "Editor/KismetCompiler/Internal"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GameplayTasks/UHT"
/I "Runtime/GameplayTasks/Classes"
/I "Runtime/GameplayTasks/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ClassViewer/UHT"
/I "Editor/ClassViewer/Public"
/I "Developer/DirectoryWatcher/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Documentation/UHT"
/I "Editor/Documentation/Public"
/I "Editor/MainFrame/Public"
/I "Runtime/SandboxFile/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SourceControl/UHT"
/I "Developer/SourceControl/Public"
/I "Developer/UncontrolledChangelists/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/UnrealEdMessages/UHT"
/I "Editor/UnrealEdMessages/Classes"
/I "Editor/UnrealEdMessages/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/BlueprintGraph/UHT"
/I "Editor/BlueprintGraph/Classes"
/I "Editor/BlueprintGraph/Public"
/I "Runtime/Online/HTTP/Public"
/I "Runtime/Online/HTTP/Internal"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/FunctionalTesting/UHT"
/I "Developer/FunctionalTesting/Classes"
/I "Developer/FunctionalTesting/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AutomationController/UHT"
/I "Developer/AutomationController/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AutomationTest/UHT"
/I "Runtime/AutomationTest/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Localization/UHT"
/I "Developer/Localization/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AudioEditor/UHT"
/I "Editor/AudioEditor/Classes"
/I "Editor/AudioEditor/Public"
/I "ThirdParty/libSampleRate/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/LevelEditor/UHT"
/I "Editor/LevelEditor/Public"
/I "Editor/CommonMenuExtensions/Public"
/I "Developer/Settings/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/VREditor/UHT"
/I "Editor/VREditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ViewportInteraction/UHT"
/I "Editor/ViewportInteraction/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/HeadMountedDisplay/UHT"
/I "Runtime/HeadMountedDisplay/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Landscape/UHT"
/I "Runtime/Landscape/Classes"
/I "Runtime/Landscape/Public"
/I "Runtime/Landscape/Internal"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DetailCustomizations/UHT"
/I "Editor/DetailCustomizations/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GraphEditor/UHT"
/I "Editor/GraphEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StructViewer/UHT"
/I "Editor/StructViewer/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MaterialEditor/UHT"
/I "Editor/MaterialEditor/Public"
/I "Runtime/NetworkFileSystem/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/UMG/UHT"
/I "Runtime/UMG/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MovieScene/UHT"
/I "Runtime/MovieScene/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/TimeManagement/UHT"
/I "Runtime/TimeManagement/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/UniversalObjectLocator/UHT"
/I "Runtime/UniversalObjectLocator/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MovieSceneTracks/UHT"
/I "Runtime/MovieSceneTracks/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/Constraints/UHT"
/I "Runtime/Experimental/Animation/Constraints/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PropertyPath/UHT"
/I "Runtime/PropertyPath/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/NavigationSystem/UHT"
/I "Runtime/NavigationSystem/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/GeometryCollectionEngine/UHT"
/I "Runtime/Experimental/GeometryCollectionEngine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ChaosSolverEngine/UHT"
/I "Runtime/Experimental/ChaosSolverEngine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DataflowCore/UHT"
/I "Runtime/Experimental/Dataflow/Core/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DataflowEngine/UHT"
/I "Runtime/Experimental/Dataflow/Engine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DataflowSimulation/UHT"
/I "Runtime/Experimental/Dataflow/Simulation/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/FieldSystemEngine/UHT"
/I "Runtime/Experimental/FieldSystem/Source/FieldSystemEngine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ISMPool/UHT"
/I "Runtime/Experimental/ISMPool/Public"
/I "Developer/MeshBuilder/Public"
/I "Runtime/MeshUtilitiesCommon/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/MSQS/UHT"
/I "Runtime/MaterialShaderQualitySettings/Classes"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/ToolMenusEditor/UHT"
/I "Editor/ToolMenusEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/StatusBar/UHT"
/I "Editor/StatusBar/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InterchangeCore/UHT"
/I "Runtime/Interchange/Core/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/InterchangeEngine/UHT"
/I "Runtime/Interchange/Engine/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/DeveloperToolSettings/UHT"
/I "Developer/DeveloperToolSettings/Classes"
/I "Developer/DeveloperToolSettings/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SubobjectDataInterface/UHT"
/I "Editor/SubobjectDataInterface/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/SubobjectEditor/UHT"
/I "Editor/SubobjectEditor/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/PhysicsUtilities/UHT"
/I "Developer/PhysicsUtilities/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/WidgetRegistration/UHT"
/I "Developer/WidgetRegistration/Public"
/I "Editor/ActorPickerMode/Public"
/I "Editor/SceneDepthPickerMode/Public"
/I "../Intermediate/Build/Win64/UnrealEditor/Inc/AnimationEditMode/UHT"
/I "Editor/AnimationEditMode/Public"
/external:W0
/external:I "ThirdParty/GuidelinesSupportLibrary/GSL-1144/include"
/external:I "ThirdParty/AtomicQueue"
/external:I "ThirdParty/RapidJSON/1.1.0"
/external:I "ThirdParty/LibTiff/Source/Win64"
/external:I "ThirdParty/LibTiff/Source"
/external:I "ThirdParty/OpenGL"
/external:I "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/INCLUDE"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/ucrt"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/shared"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/um"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/winrt"
/Yc"SharedPCH.Engine.Project.ValApi.ValExpApi.Cpp20.h"
/Fp"C:/prototipo/AURACRON/Intermediate/Build/Win64/x64/AURACRONEditor/Development/Engine/SharedPCH.Engine.Project.ValApi.ValExpApi.Cpp20.h.pch"
/Fo"C:/prototipo/AURACRON/Intermediate/Build/Win64/x64/AURACRONEditor/Development/Engine/SharedPCH.Engine.Project.ValApi.ValExpApi.Cpp20.h.obj"
/experimental:log "C:/prototipo/AURACRON/Intermediate/Build/Win64/x64/AURACRONEditor/Development/Engine/SharedPCH.Engine.Project.ValApi.ValExpApi.Cpp20.h.sarif"
/sourceDependencies "C:/prototipo/AURACRON/Intermediate/Build/Win64/x64/AURACRONEditor/Development/Engine/SharedPCH.Engine.Project.ValApi.ValExpApi.Cpp20.h.dep.json"
/Zc:inline
/nologo
/Oi
/FC
/diagnostics:caret
/c
/Gw
/Gy
/utf-8
/wd4819
/DSAL_NO_ATTRIBUTE_DECLARATIONS=1
/permissive-
/Zc:strictStrings-
/Zc:__cplusplus
/D_CRT_STDIO_LEGACY_WIDE_SPECIFIERS=1
/D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS=1
/D_WINDLL
/D_DISABLE_EXTENDED_ALIGNED_STORAGE
/Ob2
/d2ExtendedWarningInfo
/Ox
/Ot
/GF
/errorReport:prompt
/EHsc
/DPLATFORM_EXCEPTIONS_DISABLED=0
/Z7
/MD
/bigobj
/fp:fast
/Zo
/Zp8
/W4
/we4456
/we4458
/we4459
/wd4244
/wd4838
/TP
/GR-
/std:c++20
/Zc:preprocessor
/wd5054